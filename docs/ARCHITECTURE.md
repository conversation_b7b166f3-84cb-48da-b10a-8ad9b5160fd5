# AI平台架构文档

## 项目结构

```
ai-platform/
├── app/                          # 应用核心代码
│   ├── api/                      # API接口层
│   │   └── v1/                   # API v1版本
│   │       ├── task.py           # 任务管理API (已集成Celery)
│   │       ├── monitor.py        # 系统监控API
│   │       ├── files.py          # 文件管理API
│   │       └── plugins.py        # 插件管理API
│   │
│   ├── core/                     # 核心业务逻辑
│   │   ├── celery_app.py         # Celery应用配置
│   │   ├── task/                 # 任务管理
│   │   │   └── manager.py        # 原有任务管理器(保留兼容)
│   │   ├── crud/                 # 数据库操作
│   │   ├── file/                 # 文件管理
│   │   └── plugin/               # 插件管理
│   │
│   ├── workers/                  # Celery Worker相关 (新增)
│   │   ├── tasks/                # Celery任务定义
│   │   │   ├── inference.py      # 推理任务
│   │   │   └── file_download.py  # 文件下载任务
│   │   ├── manager.py            # Celery任务管理器
│   │   ├── lifecycle.py          # Worker生命周期管理
│   │   ├── monitoring/           # 监控指标收集
│   │   │   └── celery_metrics.py # Celery指标收集器
│   │   └── optimization/         # 自动优化
│   │       └── task_optimizer.py # 任务优化器
│   │
│   ├── plugins/                  # 插件系统
│   │   └── engines/              # 推理引擎
│   │       └── worker_pool.py    # Worker级引擎池
│   │
│   ├── models/                   # 数据模型
│   ├── schemas/                  # API数据模式
│   └── config/                   # 配置管理
│
├── scripts/                      # 启动脚本
│   ├── start_all.py              # 一键启动脚本
│   ├── start_celery_worker.py    # Worker启动脚本
│   ├── start_flower.py           # Flower监控启动脚本
│   └── test_celery.py            # Celery测试脚本
│
├── docs/                         # 文档
│   └── ARCHITECTURE.md           # 架构文档
│
├── README_CELERY.md              # Celery使用指南
└── main.py                       # 应用入口
```

## 核心架构

### 1. 任务处理流程

```
用户请求 → FastAPI → CeleryTaskManager → Celery队列 → Worker → 插件执行 → 结果返回
```

**详细流程：**
1. **任务创建阶段**：
   - 用户通过API创建任务
   - CeleryTaskManager快速创建Task/TaskItem记录
   - 立即返回任务信息 (< 100ms)

2. **异步处理阶段**：
   - 触发文件下载任务 (predownload_file_task)
   - 文件下载完成后自动触发推理任务 (inference_task)
   - Worker从队列获取任务并执行

3. **推理执行阶段**：
   - Worker获取引擎实例
   - 加载插件并执行推理
   - 更新任务状态和结果

### 2. 优先级队列系统

```
优先级 9 (critical)    → priority_9 队列 → 专用Worker
优先级 8-6 (high)      → priority_8-6 队列 → 优先Worker池
优先级 5-3 (normal)    → priority_5-3 队列 → 标准Worker池  
优先级 2-0 (low)       → priority_2-0 队列 → 后台Worker池
```

### 3. Worker引擎池架构

```
Worker进程 1:
├── WorkerEnginePool
│   ├── ONNX引擎实例 1
│   ├── ONNX引擎实例 2
│   └── ...
└── 任务执行器

Worker进程 2:
├── WorkerEnginePool  
│   ├── ONNX引擎实例 1
│   └── ...
└── 任务执行器
```

**特性：**
- 每个Worker进程独立管理引擎池
- 引擎实例在Worker内复用
- 自动清理空闲引擎
- 内存使用监控

### 4. 监控体系

```
监控层次：
├── Flower (主要监控界面)
│   ├── Worker状态监控
│   ├── 任务执行监控
│   ├── 队列状态监控
│   └── 实时任务流
│
└── API监控接口
    ├── /api/v1/monitor/health (系统健康检查)
    ├── /api/v1/monitor/metrics/* (性能指标)
    └── /api/v1/monitor/celery/* (Celery状态)
```

## 关键组件说明

### 1. CeleryTaskManager (app/workers/manager.py)

**职责：**
- 替换原有TaskManager
- 实现快速任务创建
- 管理异步文件下载和推理流程
- 保持API接口兼容性

**核心方法：**
- `create_task()`: 快速创建任务
- `get_task_progress()`: 获取任务进度
- `get_tasks()`: 获取任务列表

### 2. WorkerEnginePool (app/plugins/engines/worker_pool.py)

**职责：**
- Worker级别引擎池管理
- 引擎实例生命周期管理
- 内存使用监控和优化
- 统计信息收集

**核心方法：**
- `get_engine_context()`: 获取引擎上下文
- `initialize_for_worker()`: Worker初始化
- `cleanup_for_worker()`: Worker清理

### 3. CeleryMetricsCollector (app/workers/monitoring/celery_metrics.py)

**职责：**
- 任务执行指标收集
- Worker状态监控
- 性能分析和统计
- 历史数据管理

**核心方法：**
- `record_task_start()`: 记录任务开始
- `record_task_completion()`: 记录任务完成
- `get_performance_metrics()`: 获取性能指标

### 4. Celery任务 (app/workers/tasks/)

**inference_task (推理任务)：**
- 执行插件推理逻辑
- 管理引擎资源
- 更新任务状态
- 错误处理和重试

**predownload_file_task (文件下载任务)：**
- 异步文件下载
- 文件去重和缓存
- 下载完成后触发推理
- 下载失败重试

## 配置管理

### 1. Celery配置 (app/core/celery_app.py)

```python
# 队列配置
PRIORITY_QUEUES = [
    Queue(f"priority_{i}", Exchange("inference"), routing_key=f"priority.{i}")
    for i in range(10)
]

# 任务路由
task_routes = {
    "app.workers.tasks.inference.inference_task": {
        "queue": lambda task_item_id, priority=1: f"priority_{priority}",
    }
}
```

### 2. Worker配置 (app/config/celery_config.py)

```python
# Worker资源限制
CELERY_WORKER_MAX_TASKS_PER_CHILD = 1000
CELERY_WORKER_MAX_MEMORY_PER_CHILD = 2048000  # 2GB

# 引擎池配置
ENGINE_POOL_MAX_INSTANCES = 10
ENGINE_POOL_IDLE_TIMEOUT = 300  # 5分钟
```

## 部署架构

### 1. 单机部署

```
Redis (Broker) ← → FastAPI App ← → Celery Workers
                      ↓
                 PostgreSQL (数据库)
```

### 2. 分布式部署

```
Load Balancer
    ↓
FastAPI App (多实例)
    ↓
Redis Cluster (Broker)
    ↓
Celery Workers (多机器)
    ↓
PostgreSQL (主从)
```

## 性能特性

### 1. 响应时间优化

- **任务创建**: 5-30秒 → < 100ms (99%+提升)
- **异步处理**: 文件下载和推理并行执行
- **智能缓存**: 文件去重，引擎复用

### 2. 扩展性设计

- **水平扩展**: 支持多机器Worker部署
- **动态调整**: 根据负载动态增减Worker
- **资源隔离**: 不同类型任务使用不同队列

### 3. 可靠性保证

- **任务重试**: 失败任务自动重试
- **故障恢复**: Worker崩溃自动恢复
- **状态持久化**: 任务状态存储在数据库

## 监控和运维

### 1. 关键指标

- **任务指标**: 处理量、成功率、平均耗时
- **Worker指标**: CPU、内存、活跃任务数
- **队列指标**: 积压数量、处理速率
- **引擎指标**: 实例数量、内存使用

### 2. 告警机制

- **队列积压**: 高优先级队列积压 > 50
- **Worker异常**: 内存使用 > 90%
- **任务失败**: 失败率 > 10%
- **系统异常**: Worker连接中断

### 3. 运维工具

- **Flower**: Web监控界面
- **API监控**: RESTful监控接口
- **启动脚本**: 一键启动和测试
- **日志系统**: 结构化日志输出

## 最佳实践

### 1. Worker配置

- **CPU密集型**: concurrency = CPU核心数
- **IO密集型**: concurrency = CPU核心数 × 2-4
- **GPU任务**: concurrency = GPU数量

### 2. 队列设计

- **按优先级分离**: 避免低优先级阻塞高优先级
- **按资源分离**: CPU/GPU任务使用不同队列
- **按类型分离**: 图像/视频/文本任务分离

### 3. 监控策略

- **实时监控**: 关键指标实时告警
- **趋势分析**: 定期分析性能趋势
- **容量规划**: 根据历史数据规划容量

这个架构设计确保了系统的高性能、高可靠性和易扩展性，为AI平台提供了强大的任务处理能力。
