#!/usr/bin/env python3
"""
启动Flower监控界面脚本
"""
import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.config.settings import settings


def start_flower(
    port: int = 5555,
    broker_url: str = None,
    basic_auth: str = None,
    url_prefix: str = "",
    persistent: bool = True
):
    """
    启动Flower监控界面
    
    Args:
        port: 监听端口
        broker_url: Broker URL
        basic_auth: 基础认证 (username:password)
        url_prefix: URL前缀
        persistent: 是否启用持久化
    """
    # 构建启动命令
    cmd = [
        sys.executable, "-m", "flower",
        f"--port={port}",
        f"--broker={broker_url or settings.CELERY_BROKER_URL}",
    ]
    
    if basic_auth:
        cmd.append(f"--basic_auth={basic_auth}")
    
    if url_prefix:
        cmd.append(f"--url_prefix={url_prefix}")
    
    if persistent:
        cmd.append("--persistent=True")
        cmd.append("--db=flower.db")
    
    # 设置环境变量
    env = os.environ.copy()
    env["PYTHONPATH"] = str(project_root)
    
    print(f"启动Flower监控界面")
    print(f"访问地址: http://localhost:{port}{url_prefix}")
    print(f"Broker: {broker_url or settings.CELERY_BROKER_URL}")
    if basic_auth:
        print(f"认证: {basic_auth.split(':')[0]}:***")
    print("-" * 50)
    
    # 启动Flower
    import subprocess
    subprocess.run(cmd, env=env)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="启动Flower监控界面")
    
    parser.add_argument(
        "--port", "-p",
        type=int,
        default=5555,
        help="监听端口 (默认: 5555)"
    )
    
    parser.add_argument(
        "--broker",
        help="Broker URL (默认: 使用配置文件中的设置)"
    )
    
    parser.add_argument(
        "--basic-auth",
        help="基础认证，格式: username:password"
    )
    
    parser.add_argument(
        "--url-prefix",
        default="",
        help="URL前缀"
    )
    
    parser.add_argument(
        "--no-persistent",
        action="store_true",
        help="禁用持久化"
    )
    
    args = parser.parse_args()
    
    # 启动Flower
    start_flower(
        port=args.port,
        broker_url=args.broker,
        basic_auth=args.basic_auth,
        url_prefix=args.url_prefix,
        persistent=not args.no_persistent
    )


if __name__ == "__main__":
    main()
