#!/usr/bin/env python3
"""
MVC架构 + Celery测试脚本
快速测试新架构的功能
"""
import sys
import time
import requests
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def check_redis():
    """检查Redis连接"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis连接正常")
        return True
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return False


def check_api():
    """检查API服务"""
    try:
        response = requests.get("http://localhost:8000/api/v1/monitor/health/basic", timeout=5)
        if response.status_code == 200:
            print("✅ API服务正常")
            return True
        else:
            print(f"❌ API服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API服务连接失败: {e}")
        return False


def check_celery():
    """检查Celery状态"""
    try:
        response = requests.get("http://localhost:8000/api/v1/monitor/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            celery_info = data.get("data", {}).get("celery", {})
            if celery_info.get("broker_connected"):
                worker_count = celery_info.get("active_workers", 0)
                print(f"✅ Celery连接正常，活跃Worker: {worker_count}")
                return True
            else:
                print("❌ Celery Broker连接失败")
                return False
        else:
            print(f"❌ Celery健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Celery状态检查失败: {e}")
        return False


def test_task_creation():
    """测试任务创建"""
    print("\n🧪 测试任务创建...")
    
    # 准备测试数据
    task_data = {
        "items": [
            {
                "data_id": "test_001",
                "file_url": "https://httpbin.org/image/jpeg",
                "file_type": "image",
                "media_type": "image/jpeg",
                "params": {"test": True}
            }
        ],
        "plugins": [
            {
                "plugin_code": "test_plugin",
                "plugin_version": "1.0.0"
            }
        ],
        "priority": 5
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/tasks/",
            json=task_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get("data", {}).get("task_id")
            print(f"✅ 任务创建成功，任务ID: {task_id}")
            return task_id
        else:
            print(f"❌ 任务创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 任务创建异常: {e}")
        return None


def test_task_query(task_id):
    """测试任务查询"""
    if not task_id:
        return
        
    print(f"\n🔍 测试任务查询 (ID: {task_id})...")
    
    try:
        response = requests.get(f"http://localhost:8000/api/v1/tasks/{task_id}", timeout=5)
        
        if response.status_code == 200:
            result = response.json()
            task_data = result.get("data", {})
            status = task_data.get("status")
            total_items = task_data.get("total_items")
            print(f"✅ 任务查询成功")
            print(f"   状态: {status}")
            print(f"   总项目数: {total_items}")
            return True
        else:
            print(f"❌ 任务查询失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 任务查询异常: {e}")
        return False


def test_metrics():
    """测试监控指标"""
    print("\n📊 测试监控指标...")
    
    endpoints = [
        ("/api/v1/monitor/metrics/summary", "指标汇总"),
        ("/api/v1/monitor/metrics/workers", "Worker指标"),
        ("/api/v1/monitor/celery/status", "Celery状态")
    ]
    
    for endpoint, name in endpoints:
        try:
            response = requests.get(f"http://localhost:8000{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {name}获取成功")
            else:
                print(f"❌ {name}获取失败: {response.status_code}")
        except Exception as e:
            print(f"❌ {name}获取异常: {e}")


def main():
    """主函数"""
    print("🧪 Celery系统测试")
    print("=" * 50)
    
    # 基础检查
    if not check_redis():
        print("\n请先启动Redis: redis-server")
        return 1
    
    if not check_api():
        print("\n请先启动API服务: uvicorn main:app --host 0.0.0.0 --port 8000")
        return 1
    
    if not check_celery():
        print("\n请先启动Celery Worker: python scripts/start_celery_worker.py --preset normal")
        return 1
    
    # 功能测试
    task_id = test_task_creation()
    test_task_query(task_id)
    test_metrics()
    
    print("\n🎉 测试完成!")
    print("\n📋 服务地址:")
    print("   API文档:     http://localhost:8000/docs")
    print("   健康检查:    http://localhost:8000/api/v1/monitor/health")
    print("   Flower监控:  http://localhost:5555")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
