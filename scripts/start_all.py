#!/usr/bin/env python3
"""
一键启动脚本
启动完整的Celery系统
"""
import os
import sys
import time
import signal
import subprocess
from pathlib import Path
from typing import List, Dict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class ProcessManager:
    """进程管理器"""
    
    def __init__(self):
        self.processes: Dict[str, subprocess.Popen] = {}
        self.project_root = project_root
        
    def start_process(self, name: str, cmd: List[str], cwd: str = None) -> subprocess.Popen:
        """启动进程"""
        if cwd is None:
            cwd = str(self.project_root)
            
        print(f"启动 {name}: {' '.join(cmd)}")
        
        # 设置环境变量
        env = os.environ.copy()
        env["PYTHONPATH"] = str(self.project_root)
        
        process = subprocess.Popen(
            cmd,
            cwd=cwd,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        self.processes[name] = process
        print(f"✅ {name} 已启动 (PID: {process.pid})")
        return process
    
    def stop_all(self):
        """停止所有进程"""
        print("\n正在停止所有进程...")
        
        for name, process in self.processes.items():
            if process.poll() is None:  # 进程仍在运行
                print(f"停止 {name} (PID: {process.pid})")
                try:
                    process.terminate()
                    # 等待进程优雅关闭
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    print(f"强制终止 {name}")
                    process.kill()
                    process.wait()
                except Exception as e:
                    print(f"停止 {name} 时出错: {e}")
        
        print("所有进程已停止")
    
    def check_processes(self):
        """检查进程状态"""
        running = []
        stopped = []
        
        for name, process in self.processes.items():
            if process.poll() is None:
                running.append(name)
            else:
                stopped.append(name)
        
        return running, stopped


def check_redis():
    """检查Redis是否运行"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        return True
    except Exception:
        return False


def main():
    """主函数"""
    print("🚀 启动AI平台Celery系统")
    print("=" * 50)
    
    # 检查Redis
    if not check_redis():
        print("❌ Redis未运行，请先启动Redis:")
        print("   redis-server")
        return 1
    
    print("✅ Redis连接正常")
    
    # 创建进程管理器
    pm = ProcessManager()
    
    # 注册信号处理器
    def signal_handler(signum, frame):
        print(f"\n收到信号 {signum}，正在关闭...")
        pm.stop_all()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 1. 启动FastAPI应用
        pm.start_process(
            "FastAPI",
            ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
        )
        time.sleep(3)  # 等待应用启动
        
        # 2. 启动高优先级Worker
        pm.start_process(
            "Worker-High",
            [sys.executable, "scripts/start_celery_worker.py", "--preset", "high", "--concurrency", "2"]
        )
        time.sleep(2)
        
        # 3. 启动普通Worker
        pm.start_process(
            "Worker-Normal", 
            [sys.executable, "scripts/start_celery_worker.py", "--preset", "normal", "--concurrency", "4"]
        )
        time.sleep(2)
        
        # 4. 启动后台Worker
        pm.start_process(
            "Worker-Low",
            [sys.executable, "scripts/start_celery_worker.py", "--preset", "low", "--concurrency", "6"]
        )
        time.sleep(2)
        
        # 5. 启动Flower监控
        pm.start_process(
            "Flower",
            [sys.executable, "scripts/start_flower.py", "--port", "5555"]
        )
        time.sleep(2)
        
        print("\n🎉 所有服务启动完成!")
        print("=" * 50)
        print("📊 服务地址:")
        print("   FastAPI应用:    http://localhost:8000")
        print("   API文档:        http://localhost:8000/docs")
        print("   Flower监控:     http://localhost:5555")
        print("   健康检查:       http://localhost:8000/api/v1/monitor/health")
        print("\n💡 使用说明:")
        print("   - 创建任务:     POST /api/v1/tasks/")
        print("   - 查看任务:     GET  /api/v1/tasks/")
        print("   - 系统监控:     GET  /api/v1/monitor/health")
        print("   - Flower监控:   浏览器访问 http://localhost:5555")
        print("\n按 Ctrl+C 停止所有服务")
        
        # 监控进程状态
        while True:
            time.sleep(10)
            running, stopped = pm.check_processes()
            
            if stopped:
                print(f"\n⚠️  检测到进程停止: {', '.join(stopped)}")
                for name in stopped:
                    process = pm.processes[name]
                    if process.returncode != 0:
                        print(f"❌ {name} 异常退出 (返回码: {process.returncode})")
                        # 可以在这里添加重启逻辑
                
            if not running:
                print("所有进程都已停止")
                break
    
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        return 1
    finally:
        pm.stop_all()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
