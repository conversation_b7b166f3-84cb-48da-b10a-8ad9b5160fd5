#!/usr/bin/env python3
"""
启动Celery Worker脚本
支持不同的Worker配置和队列分配
"""
import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.celery_app import celery_app
from app.config.settings import settings


def start_worker(
    worker_name: str = "worker",
    queues: str = "priority_1,priority_2,priority_3,priority_4,priority_5",
    concurrency: int = None,
    loglevel: str = "INFO",
    max_tasks_per_child: int = 1000,
    max_memory_per_child: int = 2048000
):
    """
    启动Celery Worker
    
    Args:
        worker_name: Worker名称
        queues: 监听的队列列表（逗号分隔）
        concurrency: 并发数
        loglevel: 日志级别
        max_tasks_per_child: 每个子进程最大任务数
        max_memory_per_child: 每个子进程最大内存(KB)
    """
    # 构建启动命令参数
    cmd_args = [
        "worker",
        f"--hostname={worker_name}@%h",
        f"--queues={queues}",
        f"--loglevel={loglevel}",
        f"--max-tasks-per-child={max_tasks_per_child}",
        f"--max-memory-per-child={max_memory_per_child}",
        "--without-gossip",  # 禁用gossip协议，减少网络开销
        "--without-mingle",  # 禁用mingle，加快启动速度
        "--without-heartbeat",  # 禁用心跳，使用自定义健康检查
    ]
    
    # 设置并发数
    if concurrency:
        cmd_args.append(f"--concurrency={concurrency}")
    
    print(f"启动Celery Worker: {worker_name}")
    print(f"监听队列: {queues}")
    print(f"并发数: {concurrency or '自动'}")
    print(f"日志级别: {loglevel}")
    print("-" * 50)
    
    # 启动Worker
    celery_app.worker_main(cmd_args)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="启动Celery Worker")
    
    parser.add_argument(
        "--name", "-n",
        default="worker",
        help="Worker名称 (默认: worker)"
    )
    
    parser.add_argument(
        "--queues", "-Q",
        default="priority_1,priority_2,priority_3,priority_4,priority_5",
        help="监听的队列列表，逗号分隔 (默认: priority_1-5)"
    )
    
    parser.add_argument(
        "--concurrency", "-c",
        type=int,
        help="并发数 (默认: CPU核心数)"
    )
    
    parser.add_argument(
        "--loglevel", "-l",
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="日志级别 (默认: INFO)"
    )
    
    parser.add_argument(
        "--max-tasks-per-child",
        type=int,
        default=1000,
        help="每个子进程最大任务数 (默认: 1000)"
    )
    
    parser.add_argument(
        "--max-memory-per-child",
        type=int,
        default=2048000,
        help="每个子进程最大内存KB (默认: 2048000)"
    )
    
    # 预定义的Worker配置
    parser.add_argument(
        "--preset",
        choices=["high", "normal", "low", "gpu", "cpu"],
        help="预定义配置"
    )
    
    args = parser.parse_args()
    
    # 应用预定义配置
    if args.preset == "high":
        # 高优先级Worker
        args.queues = "priority_9,priority_8,priority_7"
        args.concurrency = args.concurrency or 2
        args.name = f"{args.name}_high"
    elif args.preset == "normal":
        # 普通优先级Worker
        args.queues = "priority_6,priority_5,priority_4"
        args.concurrency = args.concurrency or 4
        args.name = f"{args.name}_normal"
    elif args.preset == "low":
        # 低优先级Worker
        args.queues = "priority_3,priority_2,priority_1,priority_0"
        args.concurrency = args.concurrency or 6
        args.name = f"{args.name}_low"
    elif args.preset == "gpu":
        # GPU Worker
        args.queues = "priority_9,priority_8,priority_7,priority_6"
        args.concurrency = args.concurrency or 1
        args.name = f"{args.name}_gpu"
        # 设置GPU环境变量
        os.environ["CUDA_VISIBLE_DEVICES"] = "0"
    elif args.preset == "cpu":
        # CPU Worker
        args.queues = "priority_5,priority_4,priority_3,priority_2,priority_1"
        args.concurrency = args.concurrency or 8
        args.name = f"{args.name}_cpu"
        # 禁用GPU
        os.environ["CUDA_VISIBLE_DEVICES"] = ""
    
    # 启动Worker
    start_worker(
        worker_name=args.name,
        queues=args.queues,
        concurrency=args.concurrency,
        loglevel=args.loglevel,
        max_tasks_per_child=args.max_tasks_per_child,
        max_memory_per_child=args.max_memory_per_child
    )


if __name__ == "__main__":
    main()
