"""
任务服务 - 业务逻辑层
基于Celery的任务管理服务
"""
from datetime import datetime
from typing import List, Any, Optional

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from fastapi import HTTPException, status

from app.models.task import Task, TaskItem
from app.repository.task_repository import task_repository
from app.repository.task_item_repository import task_item_repository
from app.repository.plugin_repository import plugin_repository
from app.repository.file_repository import file_repository
from app.models.task import TaskItem
from app.schemas.task import TaskCreate, TaskResponse, TaskFilter, TaskItemCreate
from app.schemas.base import PageResponse, PageParams
from app.core.log import task_logger as logger
from app.utils.trace_context_utils import create_task_with_context, traced_async_task, add_span_attributes
from app.core.db.session import get_async_db
from app.utils.file import process_oss_url


class TaskService:
    """任务服务 - 处理任务相关的业务逻辑"""
    
    @staticmethod
    @traced_async_task(
        "celery_create_task",
        attributes={
            "component": "celery_task_manager",
            "operation": "create_task"
        }
    )
    async def create_task(
        db: AsyncSession,
        *,
        task_in: TaskCreate
    ) -> TaskResponse:
        """
        创建任务（快速响应模式）
        
        Args:
            db: 数据库会话
            task_in: 任务创建数据
            
        Returns:
            TaskResponse: 任务响应信息
        """
        try:
            # 计算总任务项数量
            total_items_count = len(task_in.items) * len(task_in.plugins)
            
            # 添加span属性
            add_span_attributes(
                total_items=len(task_in.items),
                total_plugins=len(task_in.plugins),
                priority=task_in.priority
            )
            
            # 1. 快速创建任务记录
            task_data = {
                "total_items": total_items_count,
                "status": "initializing",
                "priority": task_in.priority
            }
            task = await task_repository.create(db, obj_in=task_data)
            logger.info(f"快速创建任务记录: {task.id}, 优先级: {task_in.priority}")
            
            # 2. 异步处理任务项创建和文件下载
            create_task_with_context(
                TaskService._process_task_items_async(
                    task.id,
                    task_in.items,
                    task_in.plugins,
                    task_in.priority
                ),
                name=f"process_task_items_celery_{task.id}"
            )
            
            # 3. 立即返回任务信息（快速响应）
            return await TaskService.get_task_progress(db, task=task)
            
        except Exception as e:
            logger.error(f"创建任务失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建任务失败: {str(e)}"
            )
    
    @staticmethod
    @traced_async_task(
        "process_task_items_async_{task_id}",
        attributes={
            "component": "celery_task_manager",
            "operation": "process_task_items_async"
        }
    )
    async def _process_task_items_async(
        task_id: int,
        items: List[TaskItemCreate],
        plugins: List[Any],
        priority: int
    ):
        """
        异步处理任务项创建和文件下载
        """
        try:
            add_span_attributes(
                task_id=task_id,
                total_items=len(items),
                total_plugins=len(plugins),
                priority=priority
            )
            
            logger.info(f"开始异步处理任务 {task_id} 的任务项创建")
            
            async with get_async_db() as db:
                # 1. 获取插件信息缓存
                plugin_cache = {}
                for plugin_config in plugins:
                    plugin_key = f"{plugin_config.plugin_code}:{plugin_config.plugin_version}"
                    plugin = await plugin_repository.get_by_code_and_version(
                        db,
                        plugin_code=plugin_config.plugin_code,
                        plugin_version=plugin_config.plugin_version
                    )
                    plugin_cache[plugin_key] = plugin
                
                # 2. 批量创建TaskItem记录（不下载文件）
                task_items_to_create = []
                pending_items = 0
                completed_items = 0
                
                for item in items:
                    # 处理文件URL
                    processed_url = process_oss_url(item.file_url)
                    
                    # 检查文件是否已存在
                    existing_file = await file_repository.get_by_url(db, processed_url)
                    file_id = existing_file.id if existing_file else None
                    
                    for plugin_config in plugins:
                        plugin_key = f"{plugin_config.plugin_code}:{plugin_config.plugin_version}"
                        plugin = plugin_cache.get(plugin_key)
                        
                        # 基础任务项数据
                        task_item_data = {
                            "task_id": task_id,
                            "data_id": item.data_id,
                            "file_type": item.file_type,
                            "media_type": item.media_type,
                            "file_url": processed_url,
                            "plugin_code": plugin_config.plugin_code,
                            "plugin_version": plugin_config.plugin_version,
                            "params": item.params,
                            "file_id": file_id,
                            "from_cache": False,
                        }
                        
                        # 检查插件是否存在
                        if not plugin or plugin.status != "enabled":
                            error_message = f"插件 {plugin_config.plugin_code}:{plugin_config.plugin_version} 不可用"
                            task_item_data.update({
                                "status": "failed",
                                "error": error_message
                            })
                            completed_items += 1
                        else:
                            task_item_data["status"] = "pending"
                            pending_items += 1
                        
                        task_items_to_create.append(task_item_data)
                
                # 3. 批量创建TaskItem记录
                if task_items_to_create:
                    created_items = await task_item_repository.create_batch(db, objs_in=task_items_to_create)
                    logger.info(f"批量创建 {len(created_items)} 个任务项")
                
                # 4. 更新任务状态
                task = await task_repository.get(db, id=task_id)
                if pending_items > 0:
                    task_status = "pending"
                else:
                    task_status = "completed" if completed_items > 0 else "failed"
                
                await task_repository.update(db, db_obj=task, obj_in={
                    "status": task_status,
                    "started_at": datetime.now() if pending_items > 0 else None,
                    "completed_at": datetime.now() if pending_items == 0 else None
                })
                
                # 5. 触发文件下载和推理任务
                if pending_items > 0:
                    await CeleryTaskManager._trigger_file_downloads_and_inference(
                        created_items, priority
                    )
                
                logger.info(f"任务 {task_id} 异步处理完成，待处理项: {pending_items}, 已完成项: {completed_items}")
                
        except Exception as e:
            logger.error(f"异步处理任务项失败: {str(e)}")
            # 更新任务状态为失败
            try:
                async with get_async_db() as db:
                    task = await task_repository.get(db, id=task_id)
                    if task and task.status == "initializing":
                        await task_repository.update(db, db_obj=task, obj_in={
                            "status": "failed",
                            "error": f"任务项创建失败: {str(e)}",
                            "completed_at": datetime.now()
                        })
            except Exception as update_error:
                logger.error(f"更新任务失败状态时出错: {str(update_error)}")
    
    @staticmethod
    async def _trigger_file_downloads_and_inference(task_items: List[TaskItem], priority: int):
        """触发文件下载和推理任务"""
        from app.workers.tasks.file_download import predownload_file_task
        
        # 按文件URL分组，避免重复下载
        file_url_to_items = {}
        for item in task_items:
            if item.status == "pending":
                if item.file_url not in file_url_to_items:
                    file_url_to_items[item.file_url] = []
                file_url_to_items[item.file_url].append(item)
        
        # 触发文件下载任务
        for file_url, items in file_url_to_items.items():
            # 如果文件已存在，直接触发推理
            if items[0].file_id:
                from app.workers.tasks.inference import inference_task
                for item in items:
                    # 根据优先级发送到对应队列
                    inference_task.apply_async(
                        args=[item.id],
                        queue=f"priority_{priority}",
                        routing_key=f"priority.{priority}"
                    )
            else:
                # 触发文件下载（下载完成后会自动触发推理）
                for item in items:
                    predownload_file_task.apply_async(
                        args=[file_url, item.id, item.file_type],
                        queue="priority_5",  # 文件下载使用中等优先级
                        routing_key="priority.5"
                    )
        
        logger.info(f"触发了 {len(file_url_to_items)} 个文件的下载任务")
    
    @staticmethod
    async def get_task_progress(
        db: AsyncSession,
        *,
        task_id: Optional[int] = None,
        task: Optional[Task] = None
    ) -> TaskResponse:
        """获取任务进度信息"""
        if task is None:
            task = await task_repository.get(db, id=task_id)
            if not task:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"任务不存在: {task_id}"
                )
        
        # 获取任务项统计
        stats = await task_item_repository.get_task_stats(db, task_id=task.id)
        
        return TaskResponse(
            task_id=task.id,
            total_items=task.total_items,
            completed_items=stats.get("completed", 0),
            failed_items=stats.get("failed", 0),
            pending_items=stats.get("pending", 0),
            running_items=stats.get("running", 0),
            status=task.status,
            priority=task.priority,
            created_at=task.created_at,
            updated_at=task.updated_at,
            started_at=task.started_at,
            completed_at=task.completed_at
        )
    
    @staticmethod
    async def get_tasks(
        db: AsyncSession,
        *,
        filter_params: TaskFilter,
        page_params: PageParams
    ) -> PageResponse[TaskResponse]:
        """获取任务列表"""
        # 获取总数
        total = await task_repository.count_filtered(db, filter_params=filter_params)

        # 计算skip和limit
        skip = (page_params.page - 1) * page_params.page_size
        limit = page_params.page_size

        # 获取任务列表
        tasks = await task_repository.get_filtered(
            db,
            filter_params=filter_params,
            skip=skip,
            limit=limit
        )

        # 为每个任务获取进度信息
        result = []
        for task in tasks:
            task_info = await TaskService.get_task_progress(db, task=task)
            result.append(task_info)

        return PageResponse[TaskResponse](
            page=page_params.page,
            page_size=page_params.page_size,
            total_count=total,
            total_page=(total + limit - 1) // limit,
            items=result
        )

    @staticmethod
    async def get_task_items(
        db: AsyncSession,
        *,
        task_id: int,
        page_params: PageParams
    ) -> PageResponse["TaskItemResponse"]:
        """获取任务的任务项列表"""
        from app.schemas.task import TaskItemResponse

        # 获取总数
        total_query = select(func.count(TaskItem.id)).where(TaskItem.task_id == task_id)
        total_result = await db.execute(total_query)
        total = total_result.scalar()

        # 计算skip和limit
        skip = (page_params.page - 1) * page_params.page_size
        limit = page_params.page_size

        # 获取任务项列表
        task_items = await task_item_repository.get_by_task_id_paginated(
            db,
            task_id=task_id,
            skip=skip,
            limit=limit
        )

        # 转换为响应格式
        items = []
        for item in task_items:
            items.append(TaskItemResponse(
                id=item.id,
                task_id=item.task_id,
                data_id=item.data_id,
                file_id=item.file_id,
                file_url=item.file_url,
                file_type=item.file_type,
                media_type=item.media_type,
                plugin_code=item.plugin_code,
                plugin_version=item.plugin_version,
                params=item.params,
                status=item.status,
                result=item.result,
                error_message=item.error_message,
                created_at=item.created_at,
                updated_at=item.updated_at,
                started_at=item.started_at,
                completed_at=item.completed_at
            ))

        return PageResponse[TaskItemResponse](
            page=page_params.page,
            page_size=page_params.page_size,
            total_count=total,
            total_page=(total + limit - 1) // limit,
            items=items
        )



