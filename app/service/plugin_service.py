"""
插件管理器
负责插件的注册、获取、更新和删除
"""
import os
import shutil
from typing import Optional, Dict, Any

from fastapi import HTTPException, UploadFile, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.config.settings import settings
from app.repository.plugin_repository import plugin_repository
from app.core.plugin.parser import PluginParser, PluginParseError
from app.models.plugin import Plugin
from app.schemas.plugin import PluginUpdate, PluginFilter, PluginInfo, PluginStatus, \
    PluginCreate, PluginResponse
from app.schemas.base import PageResponse, PageParams
from app.core.log import plugin_logger as logger
from app.utils.file import get_plugin_path, get_plugin_temp_path, to_absolute_path, to_relative_path
from app.utils.version import is_newer_version


class PluginService:
    """插件服务"""

    @classmethod
    async def register_plugin(
            cls,
            db: AsyncSession,
            *,
            model_file: Optional[UploadFile] = None,
            config_file: UploadFile,
            python_file: UploadFile
    ) -> Plugin:
        """
        注册新插件
        如果是新版本插件，会自动将旧版本的状态设置为disabled
        
        Args:
            db: 数据库会话
            model_file: 模型文件（对于_vlm插件可选）
            config_file: 配置文件
            python_file: Python处理逻辑文件
            
        Returns:
            Plugin: 注册的插件对象
        
        Raises:
            HTTPException: 注册失败时抛出
        """
        # 创建临时目录
        temp_dir = get_plugin_temp_path()
        os.makedirs(temp_dir, exist_ok=True)

        # 保存临时文件
        try:
            # 保存配置文件
            config_filename = config_file.filename or "config.yaml"
            config_path = os.path.join(temp_dir, config_filename)
            with open(config_path, "wb") as f:
                f.write(await config_file.read())

            # 先解析配置获取插件编码和版本
            config = PluginParser.parse_config_file(config_path)
            plugin_code = config.get("plugin_code")
            plugin_version = config.get("plugin_version")

            if not plugin_code or not plugin_version:
                raise PluginParseError("配置文件缺少必要的插件编码或版本信息")

            # 检查插件是否已存在
            existing_plugin = await plugin_repository.get_by_code_version(
                db, plugin_code=plugin_code, plugin_version=plugin_version
            )

            if existing_plugin:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"插件 {plugin_code}:{plugin_version} 已存在"
                )

            # 检查是否是 VLM 插件
            is_vlm_plugin = plugin_code.endswith('_vlm')
            model_path = None

            # 如果不是 VLM 插件，必须提供模型文件
            if not is_vlm_plugin:
                if not model_file:
                    raise PluginParseError("非VLM插件必须提供模型文件")
                # 保存模型文件
                model_filename = model_file.filename or "model.bin"
                model_path = os.path.join(temp_dir, model_filename)
                with open(model_path, "wb") as f:
                    f.write(await model_file.read())

            # 保存Python文件
            python_filename = python_file.filename or "plugin.py"
            python_path = os.path.join(temp_dir, python_filename)
            with open(python_path, "wb") as f:
                f.write(await python_file.read())

            # 解析和验证插件
            plugin_data = PluginParser.parse_plugin(model_path, config_path, python_path)
            plugin_create_obj = PluginCreate(**plugin_data)

            # 创建插件目录
            plugin_dir = get_plugin_path(plugin_code=plugin_code, plugin_version=plugin_version)
            if os.path.exists(plugin_dir):
                shutil.rmtree(plugin_dir)
            os.makedirs(plugin_dir, exist_ok=True)

            # 移动文件到插件目录
            final_config_path = os.path.join(plugin_dir, config_filename)
            final_python_path = os.path.join(plugin_dir, python_filename)
            final_model_path = None

            shutil.move(config_path, final_config_path)
            shutil.move(python_path, final_python_path)

            if model_path:
                final_model_path = os.path.join(plugin_dir, model_filename)
                shutil.move(model_path, final_model_path)

            # 保存到数据库
            db_plugin = await plugin_repository.create_with_files(
                db,
                obj_in=plugin_create_obj,
                model_file_path=to_relative_path(final_model_path) if final_model_path else None,
                config_file_path=to_relative_path(final_config_path),
                python_file_path=to_relative_path(final_python_path),
            )

            logger.info(f"插件注册成功: {plugin_code}:{plugin_version}")

            # 禁用同一插件代码的旧版本
            old_versions = await plugin_repository.get_by_code(
                db,
                plugin_code=plugin_code,
                exclude_version=plugin_version
            )

            if old_versions:
                # 收集旧版本的ID
                old_version_ids = [p.id for p in old_versions]

                # 批量更新旧版本状态为disabled
                updated_count = await plugin_repository.update_status_by_ids(
                    db,
                    ids=old_version_ids,
                    status="disabled"
                )

                logger.info(
                    f"已自动禁用插件 {plugin_code} 的 {updated_count} 个旧版本，"
                    f"当前启用版本: {plugin_version}"
                )

            return db_plugin

        except PluginParseError as e:
            logger.error(f"插件解析错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"插件解析错误: {str(e)}"
            )
        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            logger.error(f"插件注册失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"插件注册失败: {str(e)}"
            )
        finally:
            # 清理临时目录
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)

    @classmethod
    async def sync_plugin_from_path(
            cls,
            db: AsyncSession,
            *,
            plugin_dir_path: str,
            update_if_newer: bool = True
    ) -> Plugin:
        """
        从本地路径同步插件，验证通过后更新或创建插件
        如果是新版本插件，会自动将旧版本的状态设置为disabled
        
        Args:
            db: 数据库会话
            plugin_dir_path: 插件目录路径（相对于FILE_STORAGE_PATH的路径）
            update_if_newer: 如果为True，只在当前版本比已有版本更新时才更新；为False时强制更新
            
        Returns:
            Plugin: 同步的插件对象
            
        Raises:
            HTTPException: 同步失败时抛出
        """
        try:
            # 构建绝对路径
            abs_plugin_dir = os.path.join(settings.STORAGE_PATH, plugin_dir_path)

            if not os.path.exists(abs_plugin_dir):
                raise PluginParseError(f"插件目录不存在: {abs_plugin_dir}")

            if not os.path.isdir(abs_plugin_dir):
                raise PluginParseError(f"指定路径不是一个目录: {abs_plugin_dir}")

            # 查找必要的文件
            model_path, config_path, python_path = PluginParser._find_plugin_files(abs_plugin_dir)

            # 解析配置文件获取插件编码和版本
            config = PluginParser.parse_config_file(config_path)
            plugin_code = config.get("plugin_code")
            plugin_version = config.get("plugin_version")

            if not plugin_code or not plugin_version:
                raise PluginParseError("配置文件缺少必要的插件编码或版本信息")

            # 检查是否是 VLM 插件
            is_vlm_plugin = plugin_code.endswith('_vlm')
            if is_vlm_plugin:
                model_path = None

            # 检查插件是否已存在
            existing_plugin = await plugin_repository.get_by_code_version(
                db, plugin_code=plugin_code, plugin_version=plugin_version
            )

            # 如果插件已存在且update_if_newer为True，则直接返回现有插件
            if existing_plugin and update_if_newer:
                logger.info(f"插件已存在，跳过更新: {plugin_code}:{plugin_version}")
                return existing_plugin

            # 如果启用了版本比较功能
            if update_if_newer:
                # 获取同一插件代码的所有版本
                existing_versions = await plugin_repository.get_by_code(
                    db, plugin_code=plugin_code
                )

                if existing_versions:
                    for existing in existing_versions:
                        try:
                            # 如果现有版本比当前版本更新，则跳过同步
                            if is_newer_version(existing.plugin_version, plugin_version):
                                logger.info(
                                    f"已存在更新的版本 {existing.plugin_version}，"
                                    f"跳过当前版本 {plugin_version} 的同步"
                                )
                                return existing
                        except ValueError as e:
                            # 版本号格式错误，记录警告但继续处理
                            logger.warning(
                                f"版本比较异常: {str(e)}，无法比较 {existing.plugin_version} 和 {plugin_version}"
                            )

            # 验证并解析插件
            plugin_data = PluginParser.parse_plugin(model_path, config_path, python_path)

            # 使用原始文件路径，相对于STORAGE_PATH
            rel_config_path = os.path.relpath(config_path, settings.STORAGE_PATH)
            rel_python_path = os.path.relpath(python_path, settings.STORAGE_PATH)
            rel_model_path = os.path.relpath(model_path, settings.STORAGE_PATH) if model_path else None

            # 更新或创建插件
            if existing_plugin:
                plugin_data.update({
                    "config_file_path": rel_config_path,
                    "python_file_path": rel_python_path,
                    "status": existing_plugin.status
                })
                # 只有非VLM插件才更新模型文件路径
                if not is_vlm_plugin and rel_model_path:
                    plugin_data["model_file_path"] = rel_model_path
                db_plugin = await plugin_repository.update(db, db_obj=existing_plugin, obj_in=plugin_data)
                logger.info(f"插件更新成功: {plugin_code}:{plugin_version}")
            else:
                # 新插件默认为enabled状态
                plugin_data["status"] = "enabled"
                plugin_create_obj = PluginCreate(**plugin_data)

                # 创建新插件
                db_plugin = await plugin_repository.create_with_files(
                    db,
                    obj_in=plugin_create_obj,
                    model_file_path=rel_model_path,
                    config_file_path=rel_config_path,
                    python_file_path=rel_python_path
                )
                logger.info(f"插件创建成功: {plugin_code}:{plugin_version}")

                # 禁用同一插件代码的旧版本
                old_versions = await plugin_repository.get_by_code(
                    db,
                    plugin_code=plugin_code,
                    exclude_version=plugin_version
                )

                if old_versions:
                    # 收集旧版本的ID
                    old_version_ids = [p.id for p in old_versions]

                    # 批量更新旧版本状态为disabled
                    updated_count = await plugin_repository.update_status_by_ids(
                        db,
                        ids=old_version_ids,
                        status="disabled"
                    )

                    logger.info(
                        f"已自动禁用插件 {plugin_code} 的 {updated_count} 个旧版本，"
                        f"当前启用版本: {plugin_version}"
                    )

            return db_plugin

        except PluginParseError as e:
            logger.error(f"插件解析错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"插件解析错误: {str(e)}"
            )
        except Exception as e:
            logger.error(f"插件同步失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"插件同步失败: {str(e)}"
            )

    @staticmethod
    async def get_plugins(
            db: AsyncSession,
            *,
            filter_params: PluginFilter,
            page_params: PageParams
    ) -> PageResponse[PluginResponse]:
        """
        获取插件列表
        
        Args:
            db: 数据库会话
            filter_params: 过滤条件
            page_params: 分页参数
        Returns:
            PageResponse[PluginResponse]: 分页插件列表
        """

        # 计算skip和limit
        skip = (page_params.page - 1) * page_params.page_size
        limit = page_params.page_size

        total = await plugin_repository.count_filtered(db, filter_params=filter_params)

        plugins = await plugin_repository.get_filtered(
            db, filter_params=filter_params, skip=skip, limit=limit
        )

        # 转换为响应模型
        plugin_schemas = []
        for plugin in plugins:
            plugin_schemas.append(PluginResponse.model_validate(plugin, from_attributes=True))

        return PageResponse[PluginResponse](
            page=page_params.page,
            page_size=page_params.page_size,
            total_count=total,
            total_page=(total + limit - 1) // limit,
            items=plugin_schemas
        )

    @staticmethod
    async def get_plugin(db: AsyncSession, plugin_id: int) -> Optional[Plugin]:
        """
        通过ID获取插件详情
        
        Args:
            db: 数据库会话
            plugin_id: 插件ID
            
        Returns:
            Optional[Plugin]: 插件对象或None
        
        Raises:
            HTTPException: 插件不存在时抛出
        """
        plugin = await plugin_repository.get(db, id=plugin_id)

        if not plugin or plugin.is_deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="插件不存在"
            )

        return plugin

    @staticmethod
    async def update_plugin(
            db: AsyncSession,
            *,
            plugin_id: int,
            plugin_in: PluginUpdate
    ) -> Plugin:
        """
        更新插件信息
        
        Args:
            db: 数据库会话
            plugin_id: 插件ID
            plugin_in: 更新数据
            
        Returns:
            Plugin: 更新后的插件对象
        
        Raises:
            HTTPException: 更新失败时抛出
        """
        plugin = await plugin_repository.get(db, id=plugin_id)

        if not plugin or plugin.is_deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="插件不存在"
            )

        updated_plugin = await plugin_repository.update(db, db_obj=plugin, obj_in=plugin_in)
        logger.info(f"插件更新成功: {plugin.plugin_code}:{plugin.plugin_version}")

        return updated_plugin

    @classmethod
    async def delete_plugin(cls, db: AsyncSession, plugin_id: int) -> bool:
        """
        删除插件
        
        Args:
            db: 数据库会话
            plugin_id: 插件ID
            
        Returns:
            bool: 删除是否成功
            
        Raises:
            HTTPException: 删除失败时抛出
        """
        plugin = await plugin_repository.get(db, id=plugin_id)

        if not plugin or plugin.is_deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="插件不存在"
            )

        # 软删除数据库记录
        await plugin_repository.soft_delete(db, db_obj=plugin)

        # 重命名插件目录，添加-deleted后缀
        old_dir = get_plugin_path(plugin_code=plugin.plugin_code, plugin_version=plugin.plugin_version)
        new_dir = get_plugin_path(plugin_code=plugin.plugin_code, plugin_version=plugin.plugin_version, is_deleted=True)

        if os.path.exists(old_dir):
            # 如果目标目录已存在，先删除
            if os.path.exists(new_dir):
                shutil.rmtree(new_dir)
            # 重命名目录
            shutil.move(old_dir, new_dir)

            # 更新数据库中的文件路径
            model_filename = os.path.basename(plugin.model_file_path)
            config_filename = os.path.basename(plugin.config_file_path)
            python_filename = os.path.basename(plugin.python_file_path)

            await plugin_repository.update(db, db_obj=plugin, obj_in={
                "model_file_path": os.path.join(settings.PLUGIN_STORAGE_PATH, plugin.plugin_code,
                                                f"{plugin.plugin_version}-deleted", model_filename),
                "config_file_path": os.path.join(settings.PLUGIN_STORAGE_PATH, plugin.plugin_code,
                                                 f"{plugin.plugin_version}-deleted", config_filename),
                "python_file_path": os.path.join(settings.PLUGIN_STORAGE_PATH, plugin.plugin_code,
                                                 f"{plugin.plugin_version}-deleted", python_filename)
            })

        logger.info(f"插件删除成功: {plugin.plugin_code}:{plugin.plugin_version}")
        return True

    @staticmethod
    async def get_plugin_info(
            db: AsyncSession,
            *,
            plugin_code: str,
            plugin_version: str,
            absolute_path: bool = False
    ) -> Dict[str, Any]:
        """
        获取插件调度信息
        
        Args:
            db: 数据库会话
            plugin_code: 插件编码
            plugin_version: 插件版本
            absolute_path: 是否返回绝对路径，默认为False
            
        Returns:
            Dict[str, Any]: 用于调度的插件信息
            
        Raises:
            HTTPException: 插件不存在或状态异常时抛出
        """
        # 从数据库查询插件
        plugin = await plugin_repository.get_by_code_version(
            db,
            plugin_code=plugin_code,
            plugin_version=plugin_version
        )

        if not plugin:
            logger.error(f"插件不存在: {plugin_code}:{plugin_version}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"插件不存在: {plugin_code}:{plugin_version}"
            )

        if plugin.is_deleted or plugin.status == PluginStatus.DISABLED:
            logger.error(
                f"插件状态异常: {plugin_code}:{plugin_version}, 状态: {plugin.status}, 已删除: {plugin.is_deleted}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"插件状态异常，无法执行: {plugin_code}:{plugin_version}"
            )

        # 转换为 PluginInfo 字典
        plugin_info = PluginInfo.model_validate(plugin, from_attributes=True).model_dump()

        # 根据 absolute_path 参数决定是否将路径转换为绝对路径
        if absolute_path:
            if plugin_info.get('model_file_path'):
                plugin_info['model_file_path'] = to_absolute_path(plugin_info['model_file_path'])

            if plugin_info.get('python_file_path'):
                plugin_info['python_file_path'] = to_absolute_path(plugin_info['python_file_path'])

            if plugin_info.get('config_file_path'):
                plugin_info['config_file_path'] = to_absolute_path(plugin_info['config_file_path'])

        return plugin_info


# 创建全局插件服务实例
plugin_service = PluginService()
