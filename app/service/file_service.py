"""
文件管理器
提供文件上传、下载、删除和临时URL生成等高级接口
"""
from typing import Optional, List
import os
import tempfile

from fastapi import UploadFile, HTTPException, status

from app.schemas.file import (
    FileType, File, FileCreate, TempUrlResponse, STSToken
)
import httpx
import asyncio
from urllib.parse import urlparse, unquote
from datetime import datetime
from app.repository.file_repository import file_repository
from app.core.db.session import get_async_db
from app.config.settings import settings
from app.utils.file import (
    calculate_file_hash,
    get_mime_type,
    get_file_type,
    build_storage_path,
    extract_metadata,
    process_oss_url,
    to_absolute_path,
    to_relative_path,
    is_oss_url,
    extract_oss_object_key
)
from app.core.log import file_logger as logger
from app.utils.oss import oss_client


class FileService:
    """文件服务，提供文件相关的业务逻辑"""

    def __init__(self):
        """初始化文件服务"""
        self.client_timeout = settings.HTTP_CLIENT_TIMEOUT

    async def upload_file(
            self,
            file: UploadFile,
            file_type: Optional[FileType] = None,
            generate_metadata: bool = True
    ) -> File:
        """
        上传文件
        
        Args:
            file: 上传的文件
            file_type: 文件类型
            generate_metadata: 是否生成元数据
            
        Returns:
            File: 文件信息
            
        Raises:
            HTTPException: 上传失败时抛出
        """
        try:
            # 读取文件内容
            content = await file.read()
            return await self.upload_content(content=content, file_name=file.filename, file_type=file_type,
                                             generate_metadata=generate_metadata)

        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"文件上传失败: {str(e)}"
            )

    async def upload_content(
            self,
            content: bytes,
            file_name: str,
            file_type: Optional[FileType] = None,
            generate_metadata: bool = True
    ) -> File:
        """
        上传文件内容
        
        Args:
            content: 文件内容字节
            file_name: 文件名
            file_type: 文件类型
            generate_metadata: 是否生成元数据
            
        Returns:
            File: 文件信息
            
        Raises:
            HTTPException: 上传失败时抛出
        """
        try:
            # 计算文件哈希值
            file_hash = calculate_file_hash(content)

            # 检查文件是否已存在
            async with get_async_db() as session:
                existing_file = await file_repository.get_by_hash(session, file_hash)
                if existing_file and not existing_file.is_deleted:
                    return File(
                        id=existing_file.id,
                        size=existing_file.size,
                        file_type=existing_file.file_type,
                        media_type=existing_file.media_type,
                        file_metadata=existing_file.file_metadata,
                        content_type=existing_file.content_type,
                        url=existing_file.url,
                        file_hash=existing_file.file_hash,
                        created_at=existing_file.created_at,
                        updated_at=existing_file.updated_at
                    )

            # 获取文件类型（如果未指定）
            if not file_type:
                content_type = get_mime_type(file_name)
                file_type = get_file_type(content_type)

            # 获取MIME类型
            content_type = get_mime_type(file_name)

            # 生成OSS存储路径
            oss_path = build_storage_path(
                file_type=file_type,
                file_hash=file_hash,
                file_name=file_name,
                for_oss=True
            )

            # 上传到OSS
            headers = {"Content-Type": content_type} if content_type else None
            await oss_client.upload_stream(
                object_key=oss_path,
                data=content,
                headers=headers
            )

            # 生成OSS访问URL
            url = f"https://{settings.OSS_BUCKET_NAME}.{settings.OSS_ENDPOINT}/{oss_path}"

            # 提取文件元数据
            metadata = None  # 默认为 None
            if generate_metadata:
                # 对于需要提取元数据的文件，先保存到临时文件
                with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                    temp_file.write(content)
                    temp_file_path = temp_file.name

                try:
                    # 提取元数据
                    metadata = await extract_metadata(temp_file_path, file_type)
                finally:
                    # 清理临时文件
                    os.unlink(temp_file_path)

            # 创建文件记录
            file_create = FileCreate(
                url=url,
                size=len(content),
                file_type=file_type,
                file_metadata=metadata,  # 直接使用 metadata，可能为 None
                content_type=content_type,
                file_hash=file_hash,
                oss_path=oss_path,
                platform="aliyun-oss-1",
                is_deleted=False
            )

            # 保存到数据库
            async with get_async_db() as session:
                file_db = await file_repository.create_file(session, file_create)

            return File(
                id=file_db.id,
                size=file_db.size,
                file_type=file_db.file_type,
                media_type=file_db.media_type,
                file_metadata=file_db.file_metadata,
                content_type=file_db.content_type,
                url=file_db.url,
                file_hash=file_db.file_hash,
                local_path=file_db.local_path,
                oss_path=file_db.oss_path,
                created_at=file_db.created_at,
                updated_at=file_db.updated_at
            )

        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"文件上传失败: {str(e)}"
            )

    async def download_from_url(
            self,
            url: str,
            file_type: Optional[FileType] = None,
            save_to_storage: bool = True,
            file_name: Optional[str] = None
    ) -> File:
        """
        从URL下载文件
        
        Args:
            url: 文件URL
            file_type: 文件类型
            save_to_storage: 是否保存到永久存储
            file_name: 文件名
            
        Returns:
            File: 文件信息
            
        Raises:
            HTTPException: 下载失败时抛出
        """
        try:
            # 1. 处理URL，获取原始OSS URL
            original_url = process_oss_url(url)

            # 获取或推断文件名
            if not file_name:
                parsed_url = urlparse(original_url)
                file_name = os.path.basename(unquote(original_url))
                if not file_name:
                    file_name = f"file_{datetime.now().strftime('%Y%m%d%H%M%S')}"

            # 2. 查询数据库中是否存在记录
            async with get_async_db() as session:
                existing_file = await file_repository.get_by_url(session, original_url)

                # 3. 处理已存在的记录
                if existing_file and not existing_file.is_deleted:

                    # 检查本地文件是否存在
                    if existing_file.local_path and os.path.exists(to_absolute_path(existing_file.local_path)):
                        logger.info(f"文件已存在，直接返回: {original_url}")
                        return File(
                            id=existing_file.id,
                            size=existing_file.size,
                            file_type=existing_file.file_type,
                            media_type=existing_file.media_type,
                            file_metadata=existing_file.file_metadata,
                            content_type=existing_file.content_type,
                            url=existing_file.url,
                            file_hash=existing_file.file_hash,
                            local_path=existing_file.local_path,
                            oss_path=existing_file.oss_path,
                            created_at=existing_file.created_at,
                            updated_at=existing_file.updated_at
                        )

                    # 本地文件不存在，重新下载
                    content, content_type = await self._download_file(url)

                    # 生成本地存储路径
                    if not existing_file.local_path:
                        # 将字符串转换为枚举实例
                        existing_file_type = FileType(existing_file.file_type)

                        # 获取相对路径
                        relative_path = build_storage_path(
                            file_type=existing_file_type,
                            file_hash=existing_file.file_hash,
                            file_name=file_name,
                            for_oss=False,
                            absolute=False
                        )
                        # 存储相对路径
                        db_local_path = relative_path
                        # 使用绝对路径进行文件操作
                        full_local_path = to_absolute_path(relative_path)
                    else:
                        # 如果已有路径是相对路径，转换为绝对路径用于文件操作
                        if not os.path.isabs(existing_file.local_path):
                            db_local_path = existing_file.local_path
                            full_local_path = to_absolute_path(existing_file.local_path)
                        else:
                            # 如果已有路径是绝对路径，保持不变用于文件操作，但存储为相对路径
                            full_local_path = existing_file.local_path
                            # 尝试转换为相对路径
                            db_local_path = to_relative_path(existing_file.local_path)

                    # 保存文件到本地
                    os.makedirs(os.path.dirname(full_local_path), exist_ok=True)
                    with open(full_local_path, 'wb') as f:
                        f.write(content)

                    # 提取元数据
                    metadata = None
                    if save_to_storage:
                        metadata = await extract_metadata(full_local_path, existing_file.file_type)

                    # 更新文件记录
                    existing_file.local_path = db_local_path  # 保存相对路径
                    existing_file.size = len(content)
                    existing_file.content_type = content_type or existing_file.content_type
                    existing_file.file_metadata = metadata
                    existing_file.updated_at = datetime.now()

                    await session.commit()
                    await session.refresh(existing_file)

                    return File(
                        id=existing_file.id,
                        size=existing_file.size,
                        file_type=existing_file.file_type,
                        media_type=existing_file.media_type,
                        file_metadata=existing_file.file_metadata,
                        content_type=existing_file.content_type,
                        url=existing_file.url,
                        file_hash=existing_file.file_hash,
                        local_path=existing_file.local_path,
                        oss_path=existing_file.oss_path,
                        created_at=existing_file.created_at,
                        updated_at=existing_file.updated_at
                    )

            # 4. 处理新文件
            # 下载文件
            content, content_type = await self._download_file(url)

            # 获取文件类型
            if not file_type:
                mime_type = content_type or get_mime_type(file_name)
                file_type = get_file_type(mime_type)  # 这里已经返回 FileType 枚举实例

            # 计算文件哈希
            file_hash = calculate_file_hash(content)

            # 生成本地存储路径
            relative_path = build_storage_path(
                file_type=file_type,
                file_hash=file_hash,
                file_name=file_name,
                for_oss=False,
                absolute=False
            )
            # 存储相对路径
            db_local_path = relative_path
            # 使用绝对路径进行文件操作
            full_local_path = to_absolute_path(relative_path)

            # 保存文件到本地
            os.makedirs(os.path.dirname(full_local_path), exist_ok=True)
            with open(full_local_path, 'wb') as f:
                f.write(content)

            # 提取元数据
            metadata = None
            if save_to_storage:
                metadata = await extract_metadata(full_local_path, file_type)

            # 创建文件记录
            file_create = FileCreate(
                url=original_url,  # 使用原始URL
                size=len(content),
                file_type=file_type.value,  # 使用枚举值
                file_metadata=metadata,  # 直接使用提取的元数据，可能为 None
                content_type=content_type,
                file_hash=file_hash,
                local_path=db_local_path,  # 使用相对路径
                oss_path="",  # 本地下载的文件没有OSS路径
                platform="aliyun-oss-1",  # 添加必需的 platform 字段
                is_deleted=False
            )

            # 保存到数据库
            async with get_async_db() as session:
                file_db = await file_repository.create_file(session, file_create)

            return File(
                id=file_db.id,
                size=file_db.size,
                file_type=file_db.file_type,
                media_type=file_db.media_type,
                file_metadata=file_db.file_metadata,  # 使用 file_metadata
                content_type=file_db.content_type,
                url=file_db.url,
                file_hash=file_db.file_hash,
                local_path=file_db.local_path,
                oss_path=file_db.oss_path,
                created_at=file_db.created_at,
                updated_at=file_db.updated_at
            )
        except Exception as e:
            logger.error(f"文件下载失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"文件下载失败: {str(e)}"
            )

    async def batch_download_from_urls(
            self,
            urls: List[str],
            file_type: Optional[FileType] = None,
            save_to_storage: bool = True
    ) -> List[File]:
        """
        批量从URL下载文件
        
        Args:
            urls: 文件URL列表
            file_type: 文件类型
            save_to_storage: 是否保存到永久存储
            
        Returns:
            List[File]: 文件信息列表
            
        Raises:
            HTTPException: 下载失败时抛出
        """
        try:
            tasks = []
            for url in urls:
                task = asyncio.create_task(
                    self.download_from_url(
                        url=url,
                        file_type=file_type,
                        save_to_storage=save_to_storage
                    )
                )
                tasks.append(task)

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 过滤掉异常并记录错误
            file_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"下载URL失败 {urls[i]}: {str(result)}")
                else:
                    file_results.append(result)

            return file_results
        except Exception as e:
            logger.error(f"批量下载文件失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"批量下载文件失败: {str(e)}"
            )

    async def get_file(self, file_id: int) -> File:
        """
        获取文件信息
        
        Args:
            file_id: 文件ID
            
        Returns:
            File: 文件信息
            
        Raises:
            HTTPException: 获取失败时抛出
        """
        async with get_async_db() as session:
            file_db = await file_repository.get_by_id(session, file_id)

            if not file_db:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"文件不存在: {file_id}"
                )

            return File(
                id=file_db.id,
                size=file_db.size,
                file_type=file_db.file_type,
                media_type=file_db.media_type,
                file_metadata=file_db.file_metadata,
                content_type=file_db.content_type,
                url=file_db.url,
                file_hash=file_db.file_hash,
                local_path=file_db.local_path,
                oss_path=file_db.oss_path,
                created_at=file_db.created_at,
                updated_at=file_db.updated_at
            )

    async def delete_file(self, file_id: int) -> bool:
        """
        删除文件（逻辑删除）
        
        Args:
            file_id: 文件ID
            
        Returns:
            bool: 删除是否成功
            
        Raises:
            HTTPException: 删除失败时抛出
        """
        try:
            # 获取文件信息
            async with get_async_db() as session:
                file_db = await file_repository.get_by_id(session, file_id)

                if not file_db:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"文件不存在: {file_id}"
                    )

                # 执行逻辑删除
                deleted = await file_repository.soft_delete(session, file_id)

                return deleted

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"删除文件失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"删除文件失败: {str(e)}"
            )

    async def generate_temp_url(
            self,
            file_id: int,
            expires_in: int = 3600
    ) -> TempUrlResponse:
        """
        生成临时访问URL和STS令牌
        
        Args:
            file_id: 文件ID
            expires_in: 过期时间（秒）
            
        Returns:
            TempUrlResponse: 临时访问信息，包含URL和STS令牌
            
        Raises:
            HTTPException: 生成失败时抛出
        """
        try:
            # 获取文件信息
            async with get_async_db() as session:
                file_db = await file_repository.get_by_id(session, file_id)

                if not file_db:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"文件不存在: {file_id}"
                    )

                if not file_db.oss_path:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="文件不在OSS中，无法生成临时访问令牌"
                    )

                # 生成STS令牌
                sts_token, expiration = await oss_client.generate_sts_token(
                    duration_seconds=expires_in
                )

                # 使用STS令牌生成预签名URL
                url, expires_at = await oss_client.generate_presigned_url(
                    object_key=file_db.oss_path,
                    expires_in=expires_in,
                    sts_token=sts_token
                )

                # 构造响应
                return TempUrlResponse(
                    url=url,
                    expires_at=expires_at,
                    sts_token=STSToken(
                        access_key_id=sts_token['AccessKeyId'],
                        access_key_secret=sts_token['AccessKeySecret'],
                        security_token=sts_token['SecurityToken'],
                        expiration=expiration
                    )
                )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"生成临时访问令牌失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"生成临时访问令牌失败: {str(e)}"
            )


    async def _download_file(self, url: str, retry_with_sts: bool = True) -> tuple[bytes, Optional[str]]:
        """
        下载文件内容，支持OSS 403错误的STS重试

        Args:
            url: 文件URL
            retry_with_sts: 是否在403错误时使用STS重试

        Returns:
            tuple[bytes, Optional[str]]: 文件内容和content-type
        """
        try:
            async with httpx.AsyncClient(timeout=self.client_timeout) as client:
                response = await client.get(url)
                response.raise_for_status()
                return response.content, response.headers.get('content-type')

        except httpx.HTTPStatusError as e:
            if e.response.status_code == 403 and retry_with_sts and is_oss_url(url):
                logger.warning(f"OSS URL访问被拒绝，尝试使用STS重试: {url}")
                try:
                    # 使用STS重新获取URL
                    object_key = extract_oss_object_key(url)
                    if object_key:
                        sts_url = await oss_client.generate_presigned_url(object_key, expires_in=3600)
                        return await self._download_file(sts_url, retry_with_sts=False)
                except Exception as sts_error:
                    logger.error(f"STS重试失败: {sts_error}")
                    raise e
            else:
                raise e
        except Exception as e:
            logger.error(f"下载文件失败: {url}, 错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"下载文件失败: {str(e)}"
            )


# 创建全局文件服务实例
file_service = FileService()
