"""
OpenObserve集成模块
支持通过OpenTelemetry将日志发送到OpenObserve
"""
import os
import logging
import base64
import multiprocessing
from typing import Optional
from opentelemetry import _logs
from opentelemetry.sdk._logs import LoggerProvider, LoggingHandler
from opentelemetry.sdk._logs.export import BatchLogRecordProcessor, SimpleLogRecordProcessor
from opentelemetry.exporter.otlp.proto.http._log_exporter import OTLPLogExporter
from opentelemetry.sdk.resources import Resource, SERVICE_NAME, SERVICE_VERSION
from app.config.settings import settings


class OpenObserveIntegration:
    """OpenObserve日志集成类"""
    
    def __init__(self):
        self.logger_provider: Optional[LoggerProvider] = None
        self.otlp_handler: Optional[LoggingHandler] = None
        self.is_enabled = False
        
    def setup(self) -> Optional[LoggingHandler]:
        """
        设置OpenObserve集成

        Returns:
            LoggingHandler: OpenTelemetry日志处理器，如果设置失败则返回None
        """
        try:
            # 检查是否已经初始化过（避免重复初始化）
            if self.is_enabled:
                print("OpenObserve集成已经启用，跳过重复初始化")
                return self.otlp_handler



            # 从Settings获取配置
            openobserve_url = settings.OPENOBSERVE_URL
            organization = settings.OPENOBSERVE_ORG
            username = settings.OPENOBSERVE_USERNAME
            password = settings.OPENOBSERVE_PASSWORD
            stream_name = settings.OPENOBSERVE_STREAM

            if not password:
                print("警告: OPENOBSERVE_PASSWORD未设置，跳过OpenObserve集成")
                return None
                
            # 构建endpoint和认证
            endpoint = f"{openobserve_url}/api/{organization}/v1/logs"
            auth_string = f"{username}:{password}"
            auth_header = base64.b64encode(auth_string.encode()).decode()
            
            # 创建资源信息
            resource = Resource.create({
                SERVICE_NAME: settings.APP_NAME,
                SERVICE_VERSION: settings.APP_VERSION,
                "environment": os.getenv("ENVIRONMENT", "local"),
                "host.name": os.getenv("HOSTNAME", "localhost"),
            })
            
            # 创建OTLP导出器
            otlp_exporter = OTLPLogExporter(
                endpoint=endpoint,
                headers={
                    "Authorization": f"Basic {auth_header}",
                    "stream-name": stream_name,
                    "Content-Type": "application/json"
                },
                timeout=5  # 5秒超时，避免启动阻塞
            )
            
            # 创建日志提供者
            self.logger_provider = LoggerProvider(resource=resource)

            # 检测是否在多进程环境中，选择合适的处理器
            # 在多进程环境下，BatchLogRecordProcessor可能导致问题
            is_multiprocess = multiprocessing.current_process().name != 'MainProcess'

            if is_multiprocess:
                # 在子进程中使用简单处理器，避免多进程问题
                processor = SimpleLogRecordProcessor(otlp_exporter)
                print(f"OpenObserve: 检测到子进程 {multiprocessing.current_process().name}，使用简单处理器")
            else:
                # 在主进程中使用批处理器，优化性能
                processor = BatchLogRecordProcessor(
                    otlp_exporter,
                    max_queue_size=1024,        # 减小队列大小
                    export_timeout_millis=5000,  # 5秒超时，避免启动阻塞
                    max_export_batch_size=256,   # 减小批量大小
                    schedule_delay_millis=3000   # 减少调度延迟
                )
                print("OpenObserve: 主进程使用批处理器")
            
            self.logger_provider.add_log_record_processor(processor)
            
            # 设置全局日志提供者
            _logs.set_logger_provider(self.logger_provider)
            
            # 创建处理器
            self.otlp_handler = LoggingHandler(
                level=logging.INFO,
                logger_provider=self.logger_provider
            )
            
            self.is_enabled = True
            print(f"OpenObserve集成成功启用: {endpoint}")
            return self.otlp_handler
            
        except Exception as e:
            print(f"OpenObserve集成失败: {e}")
            return None
    
    def create_loguru_sink(self, otlp_handler: LoggingHandler):
        """
        创建Loguru sink函数，将Loguru日志转发到OpenTelemetry
        
        Args:
            otlp_handler: OpenTelemetry日志处理器
            
        Returns:
            function: Loguru sink函数
        """
        def openobserve_sink(message):
            """将Loguru日志转发到OpenObserve"""
            try:
                record = message.record

                # 转换日志级别
                level_mapping = {
                    "TRACE": logging.DEBUG,
                    "DEBUG": logging.DEBUG,
                    "INFO": logging.INFO,
                    "SUCCESS": logging.INFO,
                    "WARNING": logging.WARNING,
                    "ERROR": logging.ERROR,
                    "CRITICAL": logging.CRITICAL
                }

                # 使用Loguru record的属性访问方式
                extra = record["extra"] if "extra" in record else {}

                # 创建标准库日志记录
                log_record = logging.LogRecord(
                    name=extra.get("name", "app"),
                    level=level_mapping.get(record["level"].name, logging.INFO),
                    pathname=record["file"].path if record["file"] else "",
                    lineno=record["line"] if record["line"] else 0,
                    msg=record["message"],
                    args=(),
                    exc_info=record["exception"] if record["exception"] else None
                )

                # 添加自定义字段（使用setattr避免类型检查问题）
                setattr(log_record, 'trace_id', extra.get("trace_id"))
                setattr(log_record, 'module', extra.get("name"))
                setattr(log_record, 'function', record["function"])
                setattr(log_record, 'thread', record["thread"].name if record["thread"] else None)
                setattr(log_record, 'process', record["process"].name if record["process"] else None)
                setattr(log_record, 'timestamp', record["time"].isoformat())

                # 发送到OpenObserve
                otlp_handler.emit(log_record)

            except Exception as e:
                # 避免日志循环，直接打印错误
                print(f"OpenObserve sink错误: {e}")
                import traceback
                traceback.print_exc()
        
        return openobserve_sink
    
    def shutdown(self):
        """关闭OpenObserve集成"""
        try:
            if self.logger_provider:
                self.logger_provider.shutdown()
                self.is_enabled = False
                print("OpenObserve集成已关闭")
        except Exception as e:
            print(f"关闭OpenObserve集成时出错: {e}")


def setup_openobserve_integration():
    """
    统一设置OpenObserve集成（日志和追踪）

    Returns:
        tuple: (log_handler, trace_enabled) 日志处理器和追踪是否启用的状态
    """
    # 设置日志集成
    log_handler = openobserve_integration.setup()

    # 设置追踪集成
    trace_enabled = False
    try:
        from app.core.telemetry.opentelemetry_trace import opentelemetry_trace

        # 如果还没有初始化，则进行初始化
        if not opentelemetry_trace.is_enabled:
            trace_enabled = opentelemetry_trace.setup()
        else:
            # 已经初始化过了，直接返回状态
            trace_enabled = True
            print("OpenTelemetry Trace集成已经启用，跳过重复初始化")

        if trace_enabled:
            print("OpenObserve Trace集成已启用")
        else:
            print("OpenObserve Trace集成未启用")

    except ImportError:
        print("OpenTelemetry Trace模块未找到，跳过trace集成")
    except Exception as e:
        print(f"OpenObserve Trace集成失败: {e}")

    return log_handler, trace_enabled


# 全局实例
openobserve_integration = OpenObserveIntegration()
