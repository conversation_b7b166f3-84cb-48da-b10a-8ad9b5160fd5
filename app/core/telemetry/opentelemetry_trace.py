"""
OpenTelemetry Trace集成模块
支持通过OpenTelemetry将分布式追踪数据发送到OpenObserve
"""
import os
import multiprocessing
from typing import Optional
from opentelemetry import trace
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor, SimpleSpanProcessor
from opentelemetry.sdk.trace.sampling import TraceIdRatioBased
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.resources import Resource, SERVICE_NAME, SERVICE_VERSION
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.instrumentation.logging import LoggingInstrumentor
from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
from opentelemetry.instrumentation.redis import RedisInstrumentor
import base64

from app.config.settings import settings


class OpenTelemetryTraceIntegration:
    """OpenTelemetry分布式追踪集成类"""
    
    def __init__(self):
        self.tracer_provider: Optional[TracerProvider] = None
        self.tracer: Optional[trace.Tracer] = None
        self.is_enabled = False
        
    def setup(self) -> bool:
        """
        设置OpenTelemetry分布式追踪

        Returns:
            bool: 是否成功启用追踪
        """
        try:
            # 检查是否已经初始化过（避免重复初始化）
            if self.is_enabled:
                # 避免循环导入，使用print
                print("OpenTelemetry Trace集成已经启用，跳过重复初始化")
                return True

            # 检查是否启用追踪功能
            if not settings.OPENTELEMETRY_TRACE_ENABLED:
                print("OpenTelemetry Trace功能已禁用")
                return False



            # 从Settings获取配置
            openobserve_url = settings.OPENOBSERVE_URL
            organization = settings.OPENOBSERVE_ORG
            username = settings.OPENOBSERVE_USERNAME
            password = settings.OPENOBSERVE_PASSWORD

            if not password:
                print("警告: OPENOBSERVE_PASSWORD未设置，跳过OpenTelemetry Trace集成")
                return False
                
            # 构建trace endpoint和认证
            trace_endpoint = f"{openobserve_url}/api/{organization}/v1/traces"
            auth_string = f"{username}:{password}"
            auth_header = base64.b64encode(auth_string.encode()).decode()
            
            # 创建资源信息
            resource = Resource.create({
                SERVICE_NAME: settings.APP_NAME,
                SERVICE_VERSION: settings.APP_VERSION,
                "environment": os.getenv("ENVIRONMENT", "local"),
                "host.name": os.getenv("HOSTNAME", "localhost"),
                "service.instance.id": f"{os.getpid()}",  # 进程ID作为实例标识
            })
            
            # 创建采样器（根据配置的采样率）
            sample_rate = settings.OPENTELEMETRY_TRACE_SAMPLE_RATE
            sampler = TraceIdRatioBased(sample_rate)

            # 创建TracerProvider
            self.tracer_provider = TracerProvider(resource=resource, sampler=sampler)
            
            # 创建OTLP导出器
            # 注意：为traces添加stream-name header，与日志保持一致
            otlp_exporter = OTLPSpanExporter(
                endpoint=trace_endpoint,
                headers={
                    "Authorization": f"Basic {auth_header}",
                    "stream-name": settings.OPENOBSERVE_STREAM,  # 使用与日志相同的stream
                    "Content-Type": "application/json"
                },
                timeout=5  # 5秒超时，避免启动阻塞
            )
            
            # 检测是否在多进程环境中，选择合适的处理器
            is_multiprocess = multiprocessing.current_process().name != 'MainProcess'
            
            if is_multiprocess:
                # 在子进程中使用简单处理器，避免多进程问题
                processor = SimpleSpanProcessor(otlp_exporter)
                print(f"OpenTelemetry Trace: 检测到子进程 {multiprocessing.current_process().name}，使用简单处理器")
            else:
                # 在主进程中使用批处理器，优化性能
                processor = BatchSpanProcessor(
                    otlp_exporter,
                    max_queue_size=1024,        # 减小队列大小
                    export_timeout_millis=5000,  # 5秒超时，避免启动阻塞
                    max_export_batch_size=256,   # 减小批量大小
                    schedule_delay_millis=3000   # 减少调度延迟
                )
                print("OpenTelemetry Trace: 主进程使用批处理器")
            
            self.tracer_provider.add_span_processor(processor)
            
            # 设置全局TracerProvider
            current_provider = trace.get_tracer_provider()
            # 检查是否是ProxyTracerProvider（默认的空实现）
            if type(current_provider).__name__ == 'ProxyTracerProvider':
                trace.set_tracer_provider(self.tracer_provider)
                print("设置了新的TracerProvider")
            else:
                print("检测到已存在的TracerProvider，使用现有的")
            
            # 创建Tracer
            self.tracer = trace.get_tracer(__name__)
            
            self.is_enabled = True
            print(f"OpenTelemetry Trace集成成功启用: {trace_endpoint}")
            return True

        except Exception as e:
            print(f"OpenTelemetry Trace集成失败: {e}")
            return False
    
    def instrument_fastapi(self, app):
        """
        为FastAPI应用添加自动instrumentation

        Args:
            app: FastAPI应用实例
        """
        try:
            if not self.is_enabled:
                from app.core.log import logger
                logger.warning("OpenTelemetry Trace未启用，跳过FastAPI instrumentation")
                return



            # 启用FastAPI自动instrumentation
            FastAPIInstrumentor.instrument_app(
                app,
                tracer_provider=self.tracer_provider,
                excluded_urls="/docs,/redoc,/openapi.json,/metrics,/health"  # 排除文档和健康检查端点
            )
            from app.core.log import logger
            logger.info("FastAPI OpenTelemetry instrumentation已启用")

        except Exception as e:
            from app.core.log import logger
            logger.error(f"FastAPI instrumentation失败: {e}")
    
    def instrument_requests(self):
        """启用requests库的自动instrumentation"""
        try:
            if not self.is_enabled:
                return



            RequestsInstrumentor().instrument(tracer_provider=self.tracer_provider)
            from app.core.log import logger
            logger.info("Requests OpenTelemetry instrumentation已启用")

        except Exception as e:
            from app.core.log import logger
            logger.error(f"Requests instrumentation失败: {e}")

    def instrument_logging(self):
        """启用日志的自动instrumentation，将trace信息注入到日志中"""
        try:
            if not self.is_enabled:
                return



            LoggingInstrumentor().instrument(tracer_provider=self.tracer_provider)
            from app.core.log import logger
            logger.info("Logging OpenTelemetry instrumentation已启用")

        except Exception as e:
            from app.core.log import logger
            logger.error(f"Logging instrumentation失败: {e}")

    def instrument_sqlalchemy(self):
        """启用SQLAlchemy的自动instrumentation"""
        try:
            if not self.is_enabled:
                return



            SQLAlchemyInstrumentor().instrument(tracer_provider=self.tracer_provider)
            from app.core.log import logger
            logger.info("SQLAlchemy OpenTelemetry instrumentation已启用")

        except Exception as e:
            from app.core.log import logger
            logger.error(f"SQLAlchemy instrumentation失败: {e}")

    def instrument_redis(self):
        """启用Redis的自动instrumentation"""
        try:
            if not self.is_enabled:
                return



            RedisInstrumentor().instrument(tracer_provider=self.tracer_provider)
            from app.core.log import logger
            logger.info("Redis OpenTelemetry instrumentation已启用")

        except Exception as e:
            from app.core.log import logger
            logger.error(f"Redis instrumentation失败: {e}")
    

    
    def create_span(self, name: str, **kwargs):
        """
        创建一个新的span

        Args:
            name: span名称
            **kwargs: 传递给tracer.start_span的其他参数

        Returns:
            Span: 新创建的span
        """
        if not self.is_enabled:
            # 如果追踪未启用，返回一个无操作的span
            return trace.NonRecordingSpan(trace.INVALID_SPAN_CONTEXT)

        # 使用全局tracer
        tracer = trace.get_tracer(__name__)
        # 使用start_as_current_span确保span被设置为当前span
        return tracer.start_as_current_span(name, **kwargs)
    
    def add_span_attributes(self, **attributes):
        """
        为当前span添加属性
        
        Args:
            **attributes: 要添加的属性键值对
        """
        try:
            current_span = trace.get_current_span()
            if current_span and current_span.is_recording():
                for key, value in attributes.items():
                    current_span.set_attribute(key, value)
        except Exception:
            pass  # 静默忽略错误，不影响主业务
    
    def add_span_event(self, name: str, attributes: dict = None):
        """
        为当前span添加事件
        
        Args:
            name: 事件名称
            attributes: 事件属性
        """
        try:
            current_span = trace.get_current_span()
            if current_span and current_span.is_recording():
                current_span.add_event(name, attributes or {})
        except Exception:
            pass  # 静默忽略错误，不影响主业务
    
    def record_exception(self, exception: Exception):
        """
        记录异常到当前span

        Args:
            exception: 要记录的异常
        """
        try:
            current_span = trace.get_current_span()
            if current_span and current_span.is_recording():
                current_span.record_exception(exception)
                current_span.set_status(trace.Status(trace.StatusCode.ERROR, str(exception)))
        except Exception:
            pass  # 静默忽略错误，不影响主业务

    def instrument_sqlalchemy(self):
        """启用SQLAlchemy的自动instrumentation"""
        try:
            if not self.is_enabled or not settings.OPENTELEMETRY_INSTRUMENT_SQLALCHEMY:
                return

            from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
            SQLAlchemyInstrumentor().instrument(tracer_provider=self.tracer_provider)
            from app.core.log import logger
            logger.info("SQLAlchemy OpenTelemetry instrumentation已启用")

        except ImportError:
            from app.core.log import logger
            logger.warning("SQLAlchemy instrumentation包未安装，跳过")
        except Exception as e:
            from app.core.log import logger
            logger.error(f"SQLAlchemy instrumentation失败: {e}")

    def instrument_redis(self):
        """启用Redis的自动instrumentation"""
        try:
            if not self.is_enabled or not settings.OPENTELEMETRY_INSTRUMENT_REDIS:
                return

            from opentelemetry.instrumentation.redis import RedisInstrumentor
            RedisInstrumentor().instrument(tracer_provider=self.tracer_provider)
            from app.core.log import logger
            logger.info("Redis OpenTelemetry instrumentation已启用")

        except ImportError:
            from app.core.log import logger
            logger.warning("Redis instrumentation包未安装，跳过")
        except Exception as e:
            from app.core.log import logger
            logger.error(f"Redis instrumentation失败: {e}")


# 创建全局实例（不自动初始化，避免阻塞模块导入）
opentelemetry_trace = OpenTelemetryTraceIntegration()

# 注意：移除了模块导入时的自动初始化，改为在应用启动时异步初始化
# 这样可以避免模块导入时的网络阻塞，大幅提升启动速度
