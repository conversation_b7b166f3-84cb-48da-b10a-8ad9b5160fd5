"""
Celery应用配置
负责创建和配置Celery应用实例
"""
from celery import Celery
from kombu import Exchange, Queue

from app.config.settings import settings
from app.core.log import task_logger as logger

# 创建Celery应用实例
celery_app = Celery(
    "ai_platform",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=[
        "app.tasks.inference",
        "app.tasks.file_download",
    ]
)

# 优先级队列配置
PRIORITY_QUEUES = []
for priority in range(10):  # 0-9优先级
    queue_name = f"priority_{priority}"
    PRIORITY_QUEUES.append(
        Queue(
            queue_name,
            Exchange("inference", type="direct"),
            routing_key=f"priority.{priority}",
            queue_arguments={"x-max-priority": 10}
        )
    )

# Celery配置
celery_app.conf.update(
    # 队列配置
    task_queues=PRIORITY_QUEUES,
    task_default_queue="priority_1",
    task_default_exchange="inference",
    task_default_exchange_type="direct",
    task_default_routing_key="priority.1",
    
    # 路由配置
    task_routes={
        "app.tasks.inference.inference_task": {
            "queue": lambda task_item_id, priority=1: f"priority_{priority}",
            "routing_key": lambda task_item_id, priority=1: f"priority.{priority}",
        },
        "app.tasks.file_download.predownload_file_task": {
            "queue": "priority_5",  # 文件下载使用中等优先级
            "routing_key": "priority.5",
        },
    },
    
    # 任务配置
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="Asia/Shanghai",
    enable_utc=True,
    
    # 任务执行配置
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    worker_prefetch_multiplier=1,
    
    # 结果配置
    result_expires=3600,  # 结果保存1小时
    result_extended=True,
    
    # Worker配置
    worker_max_tasks_per_child=1000,  # 防止内存泄漏
    worker_max_memory_per_child=2048000,  # 2GB内存限制
    
    # 监控配置
    worker_send_task_events=True,
    task_send_sent_event=True,
)

# 导入任务模块（确保任务被注册）

# 导入Worker生命周期管理

logger.info("Celery应用配置完成")
