"""
文件数据库CRUD操作
"""
from typing import List, Optional, Dict
from datetime import datetime

from sqlalchemy import select, update, func, and_
from sqlalchemy.ext.asyncio import AsyncSession

from app.repository.base_repository import BaseRepository
from app.models.file import File as FileModel
from app.schemas.file import FileCreate, FileInDB, File



class FileRepository(BaseRepository[FileModel, FileCreate, FileInDB]):
    """文件CRUD操作"""
    
    async def get_by_id(self, db: AsyncSession, file_id: int) -> Optional[FileInDB]:
        """根据ID获取文件"""
        query = select(self.model).where(
            and_(
                self.model.id == file_id,
                self.model.is_deleted == False
            )
        )
        result = await db.execute(query)
        db_obj = result.scalar_one_or_none()
        if db_obj:
            # 处理JSONB字段
            db_obj = self.process_jsonb_fields(db_obj)
            return FileInDB.model_validate(db_obj.__dict__)
        return None
    
    async def get_by_hash(self, db: AsyncSession, file_hash: str) -> Optional[FileInDB]:
        """根据文件哈希值获取文件记录"""
        query = select(self.model).where(
            and_(
                self.model.file_hash == file_hash,
                self.model.is_deleted == False
            )
        )
        result = await db.execute(query)
        db_obj = result.scalar_one_or_none()
        if db_obj:
            # 处理JSONB字段
            db_obj = self.process_jsonb_fields(db_obj)
            return FileInDB.model_validate(db_obj.__dict__)
        return None
    
    async def get_by_url(self, db: AsyncSession, url: str) -> Optional[FileModel]:
        """根据文件URL获取文件记录"""
        result = await db.execute(
            select(self.model).where(
                self.model.url == url,
                self.model.is_deleted == False
            )
        )
        db_obj = result.scalar_one_or_none()
        if db_obj:
            # 处理JSONB字段
            return self.process_jsonb_fields(db_obj)
        return None

    async def get_by_urls_batch(self, db: AsyncSession, urls: List[str]) -> Dict[str, FileModel]:
        """批量根据文件URL获取文件记录，用于性能优化"""
        if not urls:
            return {}

        # 去重URL列表
        unique_urls = list(set(urls))

        result = await db.execute(
            select(self.model).where(
                and_(
                    self.model.url.in_(unique_urls),
                    self.model.is_deleted == False
                )
            )
        )
        db_objs = result.scalars().all()

        # 构建URL到文件对象的映射
        file_map = {}
        for db_obj in db_objs:
            processed_obj = self.process_jsonb_fields(db_obj)
            file_map[processed_obj.url] = processed_obj

        return file_map
    
    async def create_file(self, db: AsyncSession, obj_in: FileCreate) -> FileInDB:
        """创建文件记录"""
        # 使用基类的create方法，自动处理ID生成
        db_obj = await self.create(db, obj_in=obj_in)
        return FileInDB.model_validate(db_obj.__dict__)
    
    async def soft_delete(self, db: AsyncSession, file_id: int) -> bool:
        """软删除文件记录"""
        query = update(self.model).where(
            and_(
                self.model.id == file_id,
                self.model.is_deleted == False
            )
        ).values(
            is_deleted=True,
            updated_at=datetime.now()
        )
        result = await db.execute(query)
        await db.commit()
        return result.rowcount > 0


# 创建全局文件Repository实例
file_repository = FileRepository(FileModel)