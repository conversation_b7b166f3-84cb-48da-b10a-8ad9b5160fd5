"""
通用CRUD基础类
"""
from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union
import json
from datetime import datetime

from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db.base import Base
from app.utils.snowflake import generate_snowflake_id
from app.core.log import logger

# 定义类型变量
ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class BaseRepository(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """
    提供基本CRUD操作的基类
    
    Args:
        model: SQLAlchemy数据库模型
    """

    def __init__(self, model: Type[ModelType]):
        """
        初始化CRUD操作类
        
        Args:
            model: 数据库模型类
        """
        self.model = model

    def ensure_dict(self, data: Any) -> Dict[str, Any]:
        """
        确保输入数据是一个字典类型
        
        Args:
            data: 任意输入数据
                
        Returns:
            Dict: 转换后的字典，如果输入为 None 则返回 None
        """
        if data is None:
            return None  # 返回 None 而不是空字典
            
        if isinstance(data, dict):
            return data
            
        if isinstance(data, str):
            try:
                return json.loads(data)
            except json.JSONDecodeError:
                logger.warning(f"无法将字符串解析为JSON: {data[:100]}...")
                return {}
                
        # 其他类型，尝试转换为字典
        try:
            return dict(data)
        except (TypeError, ValueError):
            logger.warning(f"无法将类型 {type(data)} 转换为字典")
            return {}

    def process_jsonb_fields(self, obj: Any) -> Any:
        """
        处理对象中的JSONB字段，确保它们是字典类型
        
        Args:
            obj: 数据库对象或字典
            
        Returns:
            处理后的对象
        """
        if obj is None:
            return obj
            
        # 如果是数据库模型的实例
        if hasattr(obj, '__table__'):
            for column in obj.__table__.columns:
                # 检查是否是JSONB类型的列
                if hasattr(column.type, 'python_type') and column.type.python_type == dict:
                    column_name = column.name
                    if hasattr(obj, column_name):
                        value = getattr(obj, column_name)
                        # 只有当字段不是列表类型时才进行字典转换
                        if not isinstance(value, list):
                            setattr(obj, column_name, self.ensure_dict(value))
            return obj
            
        # 如果是字典或可迭代对象
        if isinstance(obj, dict):
            for key, value in obj.items():
                # 只有当值不是列表类型时才进行字典转换
                if not isinstance(value, list):
                    obj[key] = self.process_jsonb_fields(value)
            return obj
            
        # 如果是列表
        if isinstance(obj, list):
            return [self.process_jsonb_fields(item) for item in obj]

        return obj

    async def get(self, db: AsyncSession, id: Any) -> Optional[ModelType]:
        """
        根据ID获取数据库记录
        
        Args:
            db: 数据库会话
            id: 记录ID
            
        Returns:
            Optional[ModelType]: 数据库记录或None
        """
        result = await db.execute(select(self.model).where(self.model.id == id))
        obj = result.scalars().first()
        if obj:
            return self.process_jsonb_fields(obj)
        return None

    async def get_multi(
        self,
        db: AsyncSession,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[ModelType]:
        """
        获取多条记录
        
        Args:
            db: 数据库会话
            skip: 跳过记录数
            limit: 返回记录数上限
            filters: 过滤条件字典
            
        Returns:
            List[ModelType]: 记录列表
        """
        query = select(self.model)
        
        # 添加过滤条件
        if filters:
            for field, value in filters.items():
                if hasattr(self.model, field):
                    if isinstance(value, list):
                        query = query.where(getattr(self.model, field).in_(value))
                    else:
                        query = query.where(getattr(self.model, field) == value)
        
        # 分页
        query = query.offset(skip).limit(limit)
        
        # 执行查询
        result = await db.execute(query)
        objs = result.scalars().all()
        
        # 处理JSONB字段
        return [self.process_jsonb_fields(obj) for obj in objs]

    def _preprocess_create_data(self, obj_in: Union[CreateSchemaType, Dict[str, Any]]) -> Dict[str, Any]:
        """
        预处理创建数据的公共方法

        Args:
            obj_in: 创建数据模型或字典

        Returns:
            Dict[str, Any]: 预处理后的数据字典
        """
        # 如果输入是字典，直接使用
        if isinstance(obj_in, dict):
            create_data = obj_in.copy()
        else:
            # 如果是 Pydantic 模型，使用 model_dump() 而不是 jsonable_encoder()
            # 这样可以避免 CamelModel 产生驼峰命名的键，确保与数据库字段名一致
            create_data = obj_in.model_dump()

        # 获取模型的列信息
        columns_info = {c.name: c.type for c in self.model.__table__.columns}

        # 根据模型字段类型处理数据
        for field_name, value in list(create_data.items()):
            if field_name in columns_info:
                column_type = columns_info[field_name]

                # 处理DateTime类型
                if hasattr(column_type, "python_type") and column_type.python_type == datetime:
                    if isinstance(value, str):
                        try:
                            # 尝试将ISO格式字符串转换为datetime对象
                            create_data[field_name] = datetime.fromisoformat(value.replace('Z', '+00:00').replace('T', ' '))
                        except (ValueError, AttributeError):
                            logger.warning(f"无法将字段 {field_name} 的值 '{value}' 转换为datetime")

        # 预处理输入中的JSONB字段
        return self.process_jsonb_fields(create_data)

    async def _create_model_instance(self, processed_data: Dict[str, Any], id_field: str = "id") -> ModelType:
        """
        创建模型实例的公共方法

        Args:
            processed_data: 预处理后的数据字典
            id_field: ID字段名，默认为"id"

        Returns:
            ModelType: 创建的模型实例
        """
        # 创建模型实例
        db_obj = self.model(**processed_data)

        # 使用雪花ID
        if id_field not in processed_data:
            setattr(db_obj, id_field, generate_snowflake_id())

        return db_obj

    async def create(self, db: AsyncSession, *, obj_in: Union[CreateSchemaType, Dict[str, Any]], id_field: str = "id") -> ModelType:
        """
        创建记录

        Args:
            db: 数据库会话
            obj_in: 创建数据模型或字典
            id_field: ID字段名，默认为"id"

        Returns:
            ModelType: 创建的数据库记录
        """
        # 使用公共预处理方法
        processed_data = self._preprocess_create_data(obj_in)

        # 使用公共模型实例创建方法
        db_obj = await self._create_model_instance(processed_data, id_field)

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)

        # 处理JSONB字段
        return self.process_jsonb_fields(db_obj)

    async def update(
        self,
        db: AsyncSession,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]]
    ) -> ModelType:
        """
        更新记录
        
        Args:
            db: 数据库会话
            db_obj: 要更新的数据库对象
            obj_in: 更新数据模型或字典
            
        Returns:
            ModelType: 更新后的数据库记录
        """
        obj_data = jsonable_encoder(db_obj)
        
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            # 使用 model_dump() 而不是 dict() 避免 CamelModel 产生驼峰命名的键
            update_data = obj_in.model_dump(exclude_unset=True)
        
        # 预处理更新数据中的JSONB字段
        update_data = self.process_jsonb_fields(update_data)
        
        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
                
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        
        # 处理JSONB字段
        return self.process_jsonb_fields(db_obj)

    async def create_batch(
        self,
        db: AsyncSession,
        *,
        objs_in: List[Union[CreateSchemaType, Dict[str, Any]]],
        id_field: str = "id"
    ) -> List[ModelType]:
        """
        批量创建记录

        Args:
            db: 数据库会话
            objs_in: 创建数据模型或字典列表
            id_field: ID字段名，默认为"id"

        Returns:
            List[ModelType]: 创建的数据库记录列表
        """
        if not objs_in:
            return []

        db_objs = []

        for obj_in in objs_in:
            # 使用公共预处理方法
            processed_data = self._preprocess_create_data(obj_in)

            # 使用公共模型实例创建方法
            db_obj = await self._create_model_instance(processed_data, id_field)

            db_objs.append(db_obj)

        # 批量添加到数据库
        db.add_all(db_objs)
        await db.commit()

        # 优化：使用批量查询替代逐个refresh，大幅减少数据库查询次数
        # 收集所有创建对象的ID
        created_ids = [getattr(db_obj, id_field) for db_obj in db_objs]

        # 使用一次批量查询获取所有刚创建的对象（包含数据库生成的字段）
        from sqlalchemy import select
        query = select(self.model).where(getattr(self.model, id_field).in_(created_ids))
        result = await db.execute(query)
        refreshed_objs = result.scalars().all()

        # 创建ID到对象的映射，保持原始顺序
        id_to_obj = {getattr(obj, id_field): obj for obj in refreshed_objs}
        ordered_objs = [id_to_obj[obj_id] for obj_id in created_ids]

        # 处理JSONB字段
        return [self.process_jsonb_fields(obj) for obj in ordered_objs]

    async def remove(self, db: AsyncSession, *, id: Any) -> Optional[ModelType]:
        """
        删除记录

        Args:
            db: 数据库会话
            id: 记录ID

        Returns:
            Optional[ModelType]: 删除的数据库记录或None
        """
        obj = await self.get(db=db, id=id)
        if obj:
            await db.delete(obj)
            await db.commit()
        return obj
        
    async def count(
        self,
        db: AsyncSession,
        *,
        filters: Optional[Dict[str, Any]] = None
    ) -> int:
        """
        计算符合条件的记录数
        
        Args:
            db: 数据库会话
            filters: 过滤条件字典
            
        Returns:
            int: 记录数量
        """
        query = select(func.count()).select_from(self.model)
        
        # 添加过滤条件
        if filters:
            for field, value in filters.items():
                if hasattr(self.model, field):
                    if isinstance(value, list):
                        query = query.where(getattr(self.model, field).in_(value))
                    else:
                        query = query.where(getattr(self.model, field) == value)
        
        # 执行查询
        result = await db.execute(query)
        return result.scalar()

