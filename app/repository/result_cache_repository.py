"""
缓存CRUD操作
"""
from typing import Dict, List, Optional, Any, Union, Tuple

from sqlalchemy import select, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession

from app.repository.base_repository import BaseRepository
from app.models.result_cache import ResultCache
from app.schemas.result_cache import ResultCacheCreate, ResultCacheUpdate


class ResultCacheRepository(BaseRepository[ResultCache, ResultCacheCreate, ResultCacheUpdate]):
    """缓存CRUD操作类"""
    
    async def get_by_composite_key(
        self,
        db: AsyncSession,
        *,
        file_hash: str,
        plugin_code: str,
        plugin_version: str,
        params_hash: str
    ) -> Optional[ResultCache]:
        """
        根据组合键获取缓存
        
        Args:
            db: 数据库会话
            file_hash: 文件哈希
            plugin_code: 插件编码
            plugin_version: 插件版本
            params_hash: 参数哈希
            
        Returns:
            Optional[ResultCache]: 缓存对象或None
        """
        result = await db.execute(
            select(self.model)
            .where(
                and_(
                    self.model.file_hash == file_hash,
                    self.model.plugin_code == plugin_code,
                    self.model.plugin_version == plugin_version,
                    self.model.params_hash == params_hash
                )
            )
        )
        db_obj = result.scalar_one_or_none()
        if db_obj:
            # 处理JSONB字段
            return self.process_jsonb_fields(db_obj)
        return None
    
    async def get_by_file_hash(
        self,
        db: AsyncSession,
        *,
        file_hash: str
    ) -> List[ResultCache]:
        """
        根据文件哈希获取所有缓存
        
        Args:
            db: 数据库会话
            file_hash: 文件哈希
            
        Returns:
            List[ResultCache]: 缓存对象列表
        """
        result = await db.execute(
            select(self.model)
            .where(self.model.file_hash == file_hash)
        )
        items = result.scalars().all()
        # 处理JSONB字段
        return [self.process_jsonb_fields(item) for item in items]
    
    async def get_by_plugin(
        self,
        db: AsyncSession,
        *,
        plugin_code: str,
        plugin_version: str
    ) -> List[ResultCache]:
        """
        根据插件获取所有缓存
        
        Args:
            db: 数据库会话
            plugin_code: 插件编码
            plugin_version: 插件版本
            
        Returns:
            List[ResultCache]: 缓存对象列表
        """
        result = await db.execute(
            select(self.model)
            .where(
                and_(
                    self.model.plugin_code == plugin_code,
                    self.model.plugin_version == plugin_version
                )
            )
        )
        items = result.scalars().all()
        # 处理JSONB字段
        return [self.process_jsonb_fields(item) for item in items]

    async def get_by_composite_keys_batch(
        self,
        db: AsyncSession,
        cache_keys: List[Tuple[str, str, str, str]]
    ) -> Dict[str, ResultCache]:
        """
        批量根据复合键查询缓存，用于性能优化

        Args:
            db: 数据库会话
            cache_keys: 缓存键列表 [(file_hash, plugin_code, plugin_version, params_hash), ...]

        Returns:
            Dict[str, ResultCache]: 缓存键到缓存对象的映射
        """
        if not cache_keys:
            return {}

        # 去重缓存键
        unique_keys = list(set(cache_keys))

        # 构建OR条件查询
        conditions = []
        for file_hash, plugin_code, plugin_version, params_hash in unique_keys:
            conditions.append(
                and_(
                    self.model.file_hash == file_hash,
                    self.model.plugin_code == plugin_code,
                    self.model.plugin_version == plugin_version,
                    self.model.params_hash == params_hash
                )
            )

        if not conditions:
            return {}

        query = select(self.model).where(or_(*conditions))
        result = await db.execute(query)
        caches = result.scalars().all()

        # 构建缓存键到结果的映射
        cache_map = {}
        for cache in caches:
            processed_cache = self.process_jsonb_fields(cache)
            key = f"{cache.file_hash}:{cache.plugin_code}:{cache.plugin_version}:{cache.params_hash}"
            cache_map[key] = processed_cache

        return cache_map


# 创建全局Repository实例
result_cache_repository = ResultCacheRepository(ResultCache)