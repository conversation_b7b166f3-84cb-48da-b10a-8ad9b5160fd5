"""
任务项CRUD操作
"""
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

from sqlalchemy import select, and_, or_, func, update
from sqlalchemy.ext.asyncio import AsyncSession

from app.repository.base_repository import BaseRepository
from app.models.task import TaskItem
from app.schemas.task import TaskItemCreate, TaskItemUpdate, TaskStatus


class TaskItemRepository(BaseRepository[TaskItem, TaskItemCreate, TaskItemUpdate]):
    """任务项CRUD操作类"""
    
    async def get_by_id(self, db: AsyncSession, task_item_id: int) -> Optional[TaskItem]:
        """
        根据ID获取任务项
        
        Args:
            db: 数据库会话
            task_item_id: 任务项ID
            
        Returns:
            Optional[TaskItem]: 任务项对象或None
        """
        query = select(self.model).where(self.model.id == task_item_id)
        result = await db.execute(query)
        db_obj = result.scalar_one_or_none()
        if db_obj:
            # 处理JSONB字段
            db_obj = self.process_jsonb_fields(db_obj)
        return db_obj
    
    async def get_by_task_id(
        self,
        db: AsyncSession,
        *,
        task_id: int
    ) -> List[TaskItem]:
        """
        获取任务的所有任务项
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            
        Returns:
            List[TaskItem]: 任务项列表
        """
        result = await db.execute(
            select(self.model)
            .where(self.model.task_id == task_id)
            .order_by(self.model.task_id.asc(), self.model.id.asc())
        )
        items = result.scalars().all()
        # 处理JSONB字段
        return [self.process_jsonb_fields(item) for item in items]
    
    async def get_by_task_id_paginated(
        self,
        db: AsyncSession,
        *,
        task_id: int,
        skip: int = 0,
        limit: int = 10
    ) -> List[TaskItem]:
        """
        获取任务的任务项（分页）
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            skip: 跳过记录数
            limit: 返回记录数上限
            
        Returns:
            List[TaskItem]: 任务项列表
        """
        result = await db.execute(
            select(self.model)
            .where(self.model.task_id == task_id)
            .order_by(self.model.task_id.asc(), self.model.id.asc())
            .offset(skip)
            .limit(limit)
        )
        items = result.scalars().all()
        # 处理JSONB字段
        return [self.process_jsonb_fields(item) for item in items]


    async def count_by_task_id(
            self,
            db: AsyncSession,
            task_id: int,
            status: Optional[Union[str, List[str]]] = None
    ) -> int:
        """
        按状态统计任务项数量

        Args:
            db: 数据库会话
            task_id: 任务ID
            status: 任务项状态(可选)，可以是字符串或字符串列表，为None时统计所有状态

        Returns:
            int: 符合条件的任务项数量
        """
        query = select(func.count(self.model.id)).where(self.model.task_id == task_id)

        if status:
            if isinstance(status, str):
                query = query.where(self.model.status == status)
            else:
                query = query.where(self.model.status.in_(status))

        result = await db.execute(query)
        return result.scalar_one()
    
    async def get_by_status(
        self,
        db: AsyncSession,
        *,
        task_id: int,
        status: Union[str, List[str]],
        limit: Optional[int] = None
    ) -> List[TaskItem]:
        """
        获取指定状态的任务项
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            status: 状态或状态列表
            limit: 返回数量限制
            
        Returns:
            List[TaskItem]: 任务项列表
        """
        if isinstance(status, str):
            status = [status]
            
        query = (
            select(self.model)
            .where(
                and_(
                    self.model.task_id == task_id,
                    self.model.status.in_(status)
                )
            )
            .order_by(self.model.task_id.asc(), self.model.id.asc())
        )

        if limit is not None:
            query = query.limit(limit)
            
        result = await db.execute(query)
        items = result.scalars().all()
        # 处理JSONB字段
        return [self.process_jsonb_fields(item) for item in items]
    
    async def count_by_status(
        self,
        db: AsyncSession,
        *,
        task_id: int,
        status: Union[str, List[str]]
    ) -> int:
        """
        统计指定状态的任务项数量
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            status: 状态或状态列表
            
        Returns:
            int: 任务项数量
        """
        if isinstance(status, str):
            status = [status]
            
        result = await db.execute(
            select(func.count())
            .select_from(self.model)
            .where(
                and_(
                    self.model.task_id == task_id,
                    self.model.status.in_(status)
                )
            )
        )
        return result.scalar()
    
    async def get_next_pending_item(
        self,
        db: AsyncSession,
        *,
        task_id: int
    ) -> Optional[TaskItem]:
        """
        获取任务的下一个待处理任务项
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            
        Returns:
            Optional[TaskItem]: 任务项对象或None
        """
        result = await db.execute(
            select(self.model)
            .where(
                and_(
                    self.model.task_id == task_id,
                    self.model.status == "pending"
                )
            )
            .order_by(self.model.task_id.asc(), self.model.id.asc())
            .limit(1)
        )
        item = result.scalar_one_or_none()
        # 处理JSONB字段
        if item:
            return self.process_jsonb_fields(item)
        return None

    async def update_status_by_task_id(
        self,
        db: AsyncSession,
        *,
        task_id: int,
        old_status: Union[str, List[str]],
        new_status: str
    ) -> int:
        """
        批量更新任务项状态
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            old_status: 原状态或状态列表
            new_status: 新状态
            
        Returns:
            int: 更新的记录数
        """
        if isinstance(old_status, str):
            old_status = [old_status]
            
        # 构建更新条件和数据
        update_stmt = (
            update(self.model)
            .where(
                and_(
                    self.model.task_id == task_id,
                    self.model.status.in_(old_status)
                )
            )
            .values(
                status=new_status, 
                updated_at=datetime.now(),
                completed_at=datetime.now() if new_status in ["completed", "failed", "canceled"] else None
            )
        )
        
        result = await db.execute(update_stmt)
        await db.commit()
        return result.rowcount

    async def update_direct(
        self,
        db: AsyncSession,
        *,
        id: int,
        update_data: Dict[str, Any]
    ) -> bool:
        """
        直接更新任务项（不先查询，用于性能优化）

        Args:
            db: 数据库会话
            id: 任务项ID
            update_data: 更新数据

        Returns:
            bool: 是否更新成功（影响行数 > 0）
        """
        # 构建更新语句
        update_stmt = (
            update(self.model)
            .where(self.model.id == id)
            .values(**update_data)
        )

        # 执行更新
        result = await db.execute(update_stmt)
        await db.commit()

        # 返回是否有行被更新
        return result.rowcount > 0

    async def get_by_task_and_status(
        self,
        db: AsyncSession,
        *,
        task_id: int,
        status: str
    ) -> List[TaskItem]:
        """
        获取指定任务和状态的任务项

        Args:
            db: 数据库会话
            task_id: 任务ID
            status: 任务状态

        Returns:
            List[TaskItem]: 任务项列表
        """
        result = await db.execute(
            select(self.model)
            .where(
                and_(
                    self.model.task_id == task_id,
                    self.model.status == status
                )
            )
            .order_by(self.model.id.asc())
        )
        items = result.scalars().all()
        return [self.process_jsonb_fields(item) for item in items]

    async def get_task_stats(
        self,
        db: AsyncSession,
        *,
        task_id: int
    ) -> Dict[str, int]:
        """
        获取任务的统计信息

        Args:
            db: 数据库会话
            task_id: 任务ID

        Returns:
            Dict[str, int]: 各状态的任务项数量
        """
        result = await db.execute(
            select(
                self.model.status,
                func.count(self.model.id).label('count')
            )
            .where(self.model.task_id == task_id)
            .group_by(self.model.status)
        )

        stats = {}
        for row in result:
            stats[row.status] = row.count

        return stats

    async def get_by_task_id_paginated(
        self,
        db: AsyncSession,
        *,
        task_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[TaskItem]:
        """
        分页获取指定任务的任务项

        Args:
            db: 数据库会话
            task_id: 任务ID
            skip: 跳过的记录数
            limit: 返回的记录数

        Returns:
            List[TaskItem]: 任务项列表
        """
        result = await db.execute(
            select(self.model)
            .where(self.model.task_id == task_id)
            .order_by(self.model.id.asc())
            .offset(skip)
            .limit(limit)
        )
        items = result.scalars().all()
        return [self.process_jsonb_fields(item) for item in items]


# 创建全局Repository实例
task_item_repository = TaskItemRepository(TaskItem)