"""
Celery配置设置
"""
import os
from typing import Optional

from pydantic import Field
from pydantic_settings import BaseSettings


class CelerySettings(BaseSettings):
    """Celery配置"""
    
    # Broker配置
    CELERY_BROKER_URL: str = Field(
        default="redis://localhost:6379/1",
        description="Celery消息代理URL"
    )
    
    # Result Backend配置
    CELERY_RESULT_BACKEND: str = Field(
        default="redis://localhost:6379/2", 
        description="Celery结果后端URL"
    )
    
    # 队列配置
    CELERY_TASK_DEFAULT_QUEUE: str = Field(
        default="priority_1",
        description="默认任务队列"
    )
    
    # Worker配置
    CELERY_WORKER_CONCURRENCY: Optional[int] = Field(
        default=None,
        description="Worker并发数，None表示使用CPU核心数"
    )
    
    CELERY_WORKER_MAX_TASKS_PER_CHILD: int = Field(
        default=1000,
        description="每个Worker子进程最大任务数"
    )
    
    CELERY_WORKER_MAX_MEMORY_PER_CHILD: int = Field(
        default=2048000,  # 2GB
        description="每个Worker子进程最大内存(KB)"
    )
    
    # 任务配置
    CELERY_TASK_SOFT_TIME_LIMIT: int = Field(
        default=3600,  # 1小时
        description="任务软超时时间(秒)"
    )
    
    CELERY_TASK_TIME_LIMIT: int = Field(
        default=3900,  # 65分钟
        description="任务硬超时时间(秒)"
    )
    
    # 监控配置
    CELERY_FLOWER_PORT: int = Field(
        default=5555,
        description="Flower监控端口"
    )
    
    CELERY_FLOWER_BASIC_AUTH: Optional[str] = Field(
        default=None,
        description="Flower基础认证，格式：username:password"
    )
    
    # 引擎池配置
    ENGINE_POOL_MAX_INSTANCES: int = Field(
        default=10,
        description="引擎池最大实例数"
    )
    
    ENGINE_POOL_IDLE_TIMEOUT: int = Field(
        default=300,  # 5分钟
        description="引擎空闲超时时间(秒)"
    )
    
    ENGINE_POOL_MAX_MEMORY_PER_ENGINE: int = Field(
        default=2048,  # 2GB
        description="每个引擎最大内存(MB)"
    )
    
    model_config = {
        "env_file": ".env",
        "env_prefix": "",
        "extra": "ignore"
    }


# 创建全局配置实例
celery_settings = CelerySettings()
