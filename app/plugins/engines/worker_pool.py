"""
Worker级别推理引擎池管理
每个Celery Worker进程维护独立的引擎池
"""
import asyncio
import time
import psutil
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager

from app.core.log import plugin_logger as logger
from app.plugins.engines.base import InferenceEngine
from app.plugins.engines.onnx import ONNXRuntimeEngine
from app.plugins.exceptions import (
    EngineInitException,
    EngineResourceException,
    UnsupportedEngineException
)
from app.config.celery_config import celery_settings


class WorkerEnginePool:
    """Worker级别引擎池管理器"""
    
    def __init__(self):
        """初始化Worker引擎池"""
        self.engines: Dict[str, Dict[str, Any]] = {}
        self.stats = {
            "hits": 0,
            "misses": 0,
            "creates": 0,
            "destroys": 0
        }
        self.worker_id = None
        self.max_instances = celery_settings.ENGINE_POOL_MAX_INSTANCES
        self.idle_timeout = celery_settings.ENGINE_POOL_IDLE_TIMEOUT
        self.max_memory_per_engine = celery_settings.ENGINE_POOL_MAX_MEMORY_PER_ENGINE
        
        logger.info(f"Worker引擎池初始化完成，最大实例数: {self.max_instances}")
    
    async def initialize_for_worker(self, worker_id: str):
        """为Worker进程初始化引擎池"""
        self.worker_id = worker_id
        logger.info(f"Worker {worker_id}: 引擎池初始化")
        
        # 启动定期清理任务
        asyncio.create_task(self._periodic_cleanup())
    
    async def cleanup_for_worker(self):
        """Worker关闭时清理所有引擎"""
        logger.info(f"Worker {self.worker_id}: 开始清理引擎池")
        
        for instance_key in list(self.engines.keys()):
            await self._destroy_engine_instance(instance_key)
        
        logger.info(f"Worker {self.worker_id}: 引擎池清理完成")
    
    @asynccontextmanager
    async def get_engine_context(self, plugin_info: Dict[str, Any]):
        """获取引擎上下文管理器"""
        plugin_code = plugin_info.get("plugin_code")
        plugin_version = plugin_info.get("plugin_version")
        engine = await self.get_engine(plugin_info)
        
        try:
            yield engine
        finally:
            if engine:
                # 更新最后使用时间
                instance_key = f"{plugin_code}:{plugin_version}"
                if instance_key in self.engines:
                    self.engines[instance_key]["last_used"] = time.time()
    
    async def get_engine(self, plugin_info: Dict[str, Any]) -> Optional[InferenceEngine]:
        """获取引擎实例"""
        plugin_code = plugin_info.get("plugin_code")
        plugin_version = plugin_info.get("plugin_version")
        engine_type = plugin_info.get("engine", "").lower()
        
        # simple引擎类型直接返回None
        if engine_type == "simple":
            return None
        
        instance_key = f"{plugin_code}:{plugin_version}"
        
        # 检查是否已存在可用实例
        if instance_key in self.engines:
            instance_info = self.engines[instance_key]
            if instance_info["status"] == "ready":
                self.stats["hits"] += 1
                instance_info["last_used"] = time.time()
                logger.debug(f"Worker {self.worker_id}: 引擎缓存命中 {instance_key}")
                return instance_info["engine"]
        
        self.stats["misses"] += 1
        logger.info(f"Worker {self.worker_id}: 引擎缓存未命中 {instance_key}")
        
        # 检查资源限制
        if len(self.engines) >= self.max_instances:
            await self._cleanup_idle_instances(force=True)
            if len(self.engines) >= self.max_instances:
                raise EngineResourceException(
                    f"Worker {self.worker_id}: 达到最大引擎实例数限制: {self.max_instances}"
                )
        
        # 创建新引擎实例
        return await self._create_engine_instance(instance_key, plugin_info)
    
    async def _create_engine_instance(self, instance_key: str, plugin_info: Dict[str, Any]) -> InferenceEngine:
        """创建新的引擎实例"""
        engine_type = plugin_info.get("engine", "").lower()
        model_path = plugin_info.get("model_file_path")
        config = plugin_info.get("config", {})
        
        logger.info(f"Worker {self.worker_id}: 创建引擎实例 {instance_key}, 类型: {engine_type}")
        
        try:
            # 标记为创建中
            self.engines[instance_key] = {
                "engine": None,
                "status": "creating",
                "created_at": time.time(),
                "last_used": time.time(),
                "engine_type": engine_type
            }
            
            # 根据引擎类型创建实例
            if engine_type == "onnx":
                engine = ONNXRuntimeEngine()
            else:
                raise UnsupportedEngineException(f"不支持的引擎类型: {engine_type}")
            
            # 初始化引擎
            await engine.initialize(model_path, config)
            
            # 更新实例信息
            self.engines[instance_key].update({
                "engine": engine,
                "status": "ready",
                "last_used": time.time()
            })
            
            self.stats["creates"] += 1
            logger.info(f"Worker {self.worker_id}: 引擎实例创建成功 {instance_key}")
            
            return engine
            
        except Exception as e:
            # 创建失败，清理记录
            if instance_key in self.engines:
                del self.engines[instance_key]
            
            logger.error(f"Worker {self.worker_id}: 引擎实例创建失败 {instance_key}: {str(e)}")
            raise EngineInitException(f"引擎初始化失败: {str(e)}")
    
    async def _destroy_engine_instance(self, instance_key: str):
        """销毁引擎实例"""
        if instance_key not in self.engines:
            return
        
        instance_info = self.engines[instance_key]
        engine = instance_info.get("engine")
        
        logger.info(f"Worker {self.worker_id}: 销毁引擎实例 {instance_key}")
        
        try:
            if engine:
                await engine.destroy()
        except Exception as e:
            logger.error(f"Worker {self.worker_id}: 引擎销毁失败 {instance_key}: {str(e)}")
        finally:
            del self.engines[instance_key]
            self.stats["destroys"] += 1
    
    async def _cleanup_idle_instances(self, force: bool = False):
        """清理空闲引擎实例"""
        current_time = time.time()
        to_remove = []
        
        for instance_key, instance_info in self.engines.items():
            idle_time = current_time - instance_info["last_used"]
            
            if force or idle_time > self.idle_timeout:
                to_remove.append(instance_key)
        
        for instance_key in to_remove:
            await self._destroy_engine_instance(instance_key)
        
        if to_remove:
            logger.info(f"Worker {self.worker_id}: 清理了 {len(to_remove)} 个空闲引擎实例")
    
    async def _periodic_cleanup(self):
        """定期清理任务"""
        while True:
            try:
                await asyncio.sleep(60)  # 每分钟检查一次
                await self._cleanup_idle_instances()
                await self._check_memory_usage()
            except Exception as e:
                logger.error(f"Worker {self.worker_id}: 定期清理任务异常: {str(e)}")
    
    async def _check_memory_usage(self):
        """检查内存使用情况"""
        try:
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            
            if memory_mb > self.max_memory_per_engine * len(self.engines):
                logger.warning(f"Worker {self.worker_id}: 内存使用过高 {memory_mb:.1f}MB，触发清理")
                await self._cleanup_idle_instances(force=True)
        except Exception as e:
            logger.error(f"Worker {self.worker_id}: 内存检查失败: {str(e)}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取引擎池统计信息"""
        return {
            "worker_id": self.worker_id,
            "active_engines": len(self.engines),
            "max_instances": self.max_instances,
            "stats": self.stats.copy(),
            "engines": {
                key: {
                    "status": info["status"],
                    "engine_type": info["engine_type"],
                    "created_at": info["created_at"],
                    "last_used": info["last_used"]
                }
                for key, info in self.engines.items()
            }
        }


# Worker级别的全局引擎池实例
_worker_engine_pool: Optional[WorkerEnginePool] = None


def get_worker_engine_pool() -> WorkerEnginePool:
    """获取Worker引擎池实例"""
    global _worker_engine_pool
    if _worker_engine_pool is None:
        _worker_engine_pool = WorkerEnginePool()
    return _worker_engine_pool
