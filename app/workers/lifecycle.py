"""
Celery Worker生命周期管理
处理Worker启动、关闭和健康检查
"""
import os
import time
import asyncio

from celery.signals import worker_ready, worker_shutdown, worker_process_init
from app.core.celery_app import celery_app
from app.plugins.engines.worker_pool import get_worker_engine_pool
from app.core.log import task_logger as logger


@worker_process_init.connect
def worker_process_init_handler(sender=None, **kwargs):
    """Worker进程初始化信号处理"""
    worker_id = f"{sender.hostname}_{os.getpid()}"
    logger.info(f"Worker进程初始化: {worker_id}")


@worker_ready.connect
def worker_ready_handler(sender=None, **kwargs):
    """Worker准备就绪信号处理"""
    worker_id = sender.hostname
    logger.info(f"Worker准备就绪: {worker_id}")
    
    # 异步初始化引擎池
    asyncio.create_task(initialize_worker_engine_pool(worker_id))


@worker_shutdown.connect
def worker_shutdown_handler(sender=None, **kwargs):
    """Worker关闭信号处理"""
    worker_id = sender.hostname
    logger.info(f"Worker开始关闭: {worker_id}")
    
    # 异步清理引擎池
    asyncio.create_task(cleanup_worker_engine_pool(worker_id))


async def initialize_worker_engine_pool(worker_id: str):
    """初始化Worker引擎池"""
    try:
        engine_pool = get_worker_engine_pool()
        await engine_pool.initialize_for_worker(worker_id)
        logger.info(f"Worker {worker_id}: 引擎池初始化完成")
    except Exception as e:
        logger.error(f"Worker {worker_id}: 引擎池初始化失败: {str(e)}")


async def cleanup_worker_engine_pool(worker_id: str):
    """清理Worker引擎池"""
    try:
        engine_pool = get_worker_engine_pool()
        await engine_pool.cleanup_for_worker()
        logger.info(f"Worker {worker_id}: 引擎池清理完成")
    except Exception as e:
        logger.error(f"Worker {worker_id}: 引擎池清理失败: {str(e)}")


@celery_app.task(bind=True)
def health_check_task(self):
    """Worker健康检查任务"""
    try:
        worker_id = self.request.hostname
        engine_pool = get_worker_engine_pool()
        
        # 获取引擎池状态
        stats = engine_pool.get_stats()
        
        # 检查系统资源
        import psutil
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        cpu_percent = process.cpu_percent()
        
        health_info = {
            "worker_id": worker_id,
            "status": "healthy",
            "memory_mb": round(memory_mb, 2),
            "cpu_percent": round(cpu_percent, 2),
            "engine_pool": stats,
            "timestamp": time.time()
        }
        
        logger.debug(f"Worker {worker_id}: 健康检查完成")
        return health_info
        
    except Exception as e:
        logger.error(f"Worker健康检查失败: {str(e)}")
        return {
            "worker_id": getattr(self.request, 'hostname', 'unknown'),
            "status": "unhealthy",
            "error": str(e),
            "timestamp": time.time()
        }


@celery_app.task(bind=True)
def get_worker_stats_task(self):
    """获取Worker统计信息任务"""
    try:
        worker_id = self.request.hostname
        engine_pool = get_worker_engine_pool()
        
        # 获取详细统计信息
        stats = engine_pool.get_stats()
        
        # 获取Celery Worker状态
        from celery import current_app
        inspect = current_app.control.inspect()
        
        worker_stats = {
            "worker_id": worker_id,
            "engine_pool_stats": stats,
            "celery_stats": {
                "active_tasks": len(inspect.active().get(worker_id, [])),
                "scheduled_tasks": len(inspect.scheduled().get(worker_id, [])),
                "reserved_tasks": len(inspect.reserved().get(worker_id, []))
            }
        }
        
        return worker_stats
        
    except Exception as e:
        logger.error(f"获取Worker统计信息失败: {str(e)}")
        return {"error": str(e)}


# 注册定期任务

celery_app.conf.beat_schedule = {
    # 每分钟执行健康检查
    'worker-health-check': {
        'task': 'app.core.celery_worker.health_check_task',
        'schedule': 60.0,  # 每60秒
    },
    # 每5分钟收集统计信息
    'worker-stats-collection': {
        'task': 'app.core.celery_worker.get_worker_stats_task', 
        'schedule': 300.0,  # 每300秒
    },
}
