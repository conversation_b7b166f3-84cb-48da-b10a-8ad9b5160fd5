"""
Celery监控指标收集
"""
import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict, deque

from app.core.log import task_logger as logger


@dataclass
class TaskMetrics:
    """任务执行指标"""
    task_name: str
    task_id: str
    worker_id: str
    queue_name: str
    priority: int
    start_time: float
    end_time: Optional[float] = None
    status: str = "running"  # running, completed, failed, retried
    error_message: Optional[str] = None
    retry_count: int = 0
    memory_usage_mb: Optional[float] = None
    cpu_usage_percent: Optional[float] = None


@dataclass
class WorkerMetrics:
    """Worker指标"""
    worker_id: str
    active_tasks: int
    completed_tasks: int
    failed_tasks: int
    memory_usage_mb: float
    cpu_usage_percent: float
    engine_pool_stats: Dict[str, Any]
    last_heartbeat: float


@dataclass
class QueueMetrics:
    """队列指标"""
    queue_name: str
    priority: int
    pending_tasks: int
    processing_rate: float  # 任务/秒
    average_wait_time: float  # 平均等待时间
    average_processing_time: float  # 平均处理时间


class CeleryMetricsCollector:
    """Celery指标收集器"""
    
    def __init__(self, max_history_size: int = 10000):
        """
        初始化指标收集器
        
        Args:
            max_history_size: 最大历史记录数量
        """
        self.max_history_size = max_history_size
        
        # 任务指标历史
        self.task_history: deque = deque(maxlen=max_history_size)
        self.active_tasks: Dict[str, TaskMetrics] = {}
        
        # Worker指标
        self.worker_metrics: Dict[str, WorkerMetrics] = {}
        
        # 队列指标
        self.queue_metrics: Dict[str, QueueMetrics] = {}
        
        # 统计数据
        self.stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "retried_tasks": 0,
            "average_task_duration": 0.0,
            "tasks_per_minute": 0.0
        }
        
        logger.info("Celery指标收集器初始化完成")
    
    def record_task_start(
        self,
        task_id: str,
        task_name: str,
        worker_id: str,
        queue_name: str,
        priority: int
    ):
        """记录任务开始"""
        metrics = TaskMetrics(
            task_name=task_name,
            task_id=task_id,
            worker_id=worker_id,
            queue_name=queue_name,
            priority=priority,
            start_time=time.time(),
            status="running"
        )
        
        self.active_tasks[task_id] = metrics
        self.stats["total_tasks"] += 1
        
        logger.debug(f"记录任务开始: {task_id} on {worker_id}")
    
    def record_task_completion(
        self,
        task_id: str,
        status: str,
        error_message: Optional[str] = None,
        retry_count: int = 0
    ):
        """记录任务完成"""
        if task_id not in self.active_tasks:
            logger.warning(f"任务 {task_id} 不在活跃任务列表中")
            return
        
        metrics = self.active_tasks.pop(task_id)
        metrics.end_time = time.time()
        metrics.status = status
        metrics.error_message = error_message
        metrics.retry_count = retry_count
        
        # 添加到历史记录
        self.task_history.append(metrics)
        
        # 更新统计
        if status == "completed":
            self.stats["completed_tasks"] += 1
        elif status == "failed":
            self.stats["failed_tasks"] += 1
        elif status == "retried":
            self.stats["retried_tasks"] += 1
        
        # 更新平均执行时间
        self._update_average_duration()
        
        logger.debug(f"记录任务完成: {task_id}, 状态: {status}")
    
    def record_worker_metrics(
        self,
        worker_id: str,
        active_tasks: int,
        completed_tasks: int,
        failed_tasks: int,
        memory_usage_mb: float,
        cpu_usage_percent: float,
        engine_pool_stats: Dict[str, Any]
    ):
        """记录Worker指标"""
        self.worker_metrics[worker_id] = WorkerMetrics(
            worker_id=worker_id,
            active_tasks=active_tasks,
            completed_tasks=completed_tasks,
            failed_tasks=failed_tasks,
            memory_usage_mb=memory_usage_mb,
            cpu_usage_percent=cpu_usage_percent,
            engine_pool_stats=engine_pool_stats,
            last_heartbeat=time.time()
        )
        
        logger.debug(f"记录Worker指标: {worker_id}")
    
    def record_queue_metrics(
        self,
        queue_name: str,
        priority: int,
        pending_tasks: int
    ):
        """记录队列指标"""
        # 计算处理速率和等待时间
        processing_rate = self._calculate_processing_rate(queue_name)
        avg_wait_time = self._calculate_average_wait_time(queue_name)
        avg_processing_time = self._calculate_average_processing_time(queue_name)
        
        self.queue_metrics[queue_name] = QueueMetrics(
            queue_name=queue_name,
            priority=priority,
            pending_tasks=pending_tasks,
            processing_rate=processing_rate,
            average_wait_time=avg_wait_time,
            average_processing_time=avg_processing_time
        )
        
        logger.debug(f"记录队列指标: {queue_name}")
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """获取汇总统计信息"""
        # 更新每分钟任务数
        self._update_tasks_per_minute()
        
        return {
            "overview": self.stats.copy(),
            "active_tasks_count": len(self.active_tasks),
            "workers_count": len(self.worker_metrics),
            "queues_count": len(self.queue_metrics),
            "history_size": len(self.task_history)
        }
    
    def get_worker_stats(self) -> List[Dict[str, Any]]:
        """获取Worker统计信息"""
        return [asdict(metrics) for metrics in self.worker_metrics.values()]
    
    def get_queue_stats(self) -> List[Dict[str, Any]]:
        """获取队列统计信息"""
        return [asdict(metrics) for metrics in self.queue_metrics.values()]
    
    def get_task_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取任务历史记录"""
        recent_tasks = list(self.task_history)[-limit:]
        return [asdict(task) for task in recent_tasks]
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        if not self.task_history:
            return {}
        
        # 按队列分组统计
        queue_stats = defaultdict(list)
        for task in self.task_history:
            if task.end_time:
                duration = task.end_time - task.start_time
                queue_stats[task.queue_name].append(duration)
        
        # 计算各队列的性能指标
        performance = {}
        for queue_name, durations in queue_stats.items():
            if durations:
                performance[queue_name] = {
                    "count": len(durations),
                    "avg_duration": sum(durations) / len(durations),
                    "min_duration": min(durations),
                    "max_duration": max(durations),
                    "p95_duration": self._percentile(durations, 95),
                    "p99_duration": self._percentile(durations, 99)
                }
        
        return performance
    
    def _update_average_duration(self):
        """更新平均执行时间"""
        completed_tasks = [
            task for task in self.task_history
            if task.status == "completed" and task.end_time
        ]
        
        if completed_tasks:
            total_duration = sum(
                task.end_time - task.start_time
                for task in completed_tasks
            )
            self.stats["average_task_duration"] = total_duration / len(completed_tasks)
    
    def _update_tasks_per_minute(self):
        """更新每分钟任务数"""
        current_time = time.time()
        one_minute_ago = current_time - 60
        
        recent_tasks = [
            task for task in self.task_history
            if task.start_time >= one_minute_ago
        ]
        
        self.stats["tasks_per_minute"] = len(recent_tasks)
    
    def _calculate_processing_rate(self, queue_name: str) -> float:
        """计算队列处理速率"""
        current_time = time.time()
        one_minute_ago = current_time - 60
        
        recent_tasks = [
            task for task in self.task_history
            if task.queue_name == queue_name and task.start_time >= one_minute_ago
        ]
        
        return len(recent_tasks) / 60.0  # 任务/秒
    
    def _calculate_average_wait_time(self, queue_name: str) -> float:
        """计算平均等待时间（简化实现）"""
        # 这里需要更复杂的逻辑来跟踪任务从创建到开始执行的时间
        # 暂时返回0，实际实现需要在任务创建时记录时间戳
        return 0.0
    
    def _calculate_average_processing_time(self, queue_name: str) -> float:
        """计算平均处理时间"""
        queue_tasks = [
            task for task in self.task_history
            if task.queue_name == queue_name and task.end_time and task.status == "completed"
        ]
        
        if not queue_tasks:
            return 0.0
        
        total_time = sum(task.end_time - task.start_time for task in queue_tasks)
        return total_time / len(queue_tasks)
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """计算百分位数"""
        if not data:
            return 0.0
        
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile / 100)
        return sorted_data[min(index, len(sorted_data) - 1)]


# 全局指标收集器实例
metrics_collector = CeleryMetricsCollector()
