"""
推理相关Celery任务
"""
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional

from app.core.celery_app import celery_app
from app.repository.task_item_repository import task_item_repository
from app.repository.file_repository import file_repository
from app.repository.plugin_repository import plugin_repository
from app.service.cache_service import CacheService
from app.plugins.engines.worker_pool import get_worker_engine_pool
from app.core.db import get_async_db
from app.core.log import task_logger as logger
from app.utils.trace_context_utils import traced_async_task, add_span_attributes, add_span_event
from app.workers.monitoring.celery_metrics import metrics_collector


@celery_app.task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3},
    soft_time_limit=3600,  # 1小时软超时
    time_limit=3900        # 65分钟硬超时
)
@traced_async_task(
    "inference_task_{task_item_id}",
    attributes={
        "component": "inference",
        "operation": "execute_inference"
    }
)
def inference_task(self, task_item_id: int):
    """
    推理任务
    
    Args:
        task_item_id: 任务项ID
    """
    async def _execute_inference():
        try:
            add_span_attributes(task_item_id=task_item_id)
            add_span_event("inference_task_started")

            # 记录任务开始指标
            worker_id = getattr(self.request, 'hostname', 'unknown')
            queue_name = getattr(self.request, 'delivery_info', {}).get('routing_key', 'unknown')
            priority = int(queue_name.split('.')[-1]) if 'priority.' in queue_name else 1

            metrics_collector.record_task_start(
                task_id=self.request.id,
                task_name="inference_task",
                worker_id=worker_id,
                queue_name=queue_name,
                priority=priority
            )

            logger.info(f"开始执行推理任务: TaskItem {task_item_id}")
            
            # 1. 获取任务项信息
            async with get_async_db() as db:
                task_item = await task_item_repository.get(db, id=task_item_id)
                if not task_item:
                    raise ValueError(f"TaskItem不存在: {task_item_id}")

                # 更新任务项状态为运行中
                await task_item_repository.update(
                    db,
                    db_obj=task_item,
                    obj_in={
                        "status": "running",
                        "started_at": datetime.now()
                    }
                )
                
                add_span_attributes(
                    plugin_code=task_item.plugin_code,
                    plugin_version=task_item.plugin_version,
                    file_type=task_item.file_type,
                    media_type=task_item.media_type
                )
            
            # 2. 确保文件已下载
            file_info = await _ensure_file_downloaded(task_item)
            
            # 3. 获取插件信息
            plugin_info = await _get_plugin_info(task_item)
            
            # 4. 检查缓存
            cache_result = await _check_cache(task_item, file_info)
            if cache_result:
                logger.info(f"TaskItem {task_item_id}: 使用缓存结果")
                await _update_task_item_success(task_item_id, cache_result, from_cache=True)
                return cache_result
            
            # 5. 执行推理
            add_span_event("inference_execution_started")
            result = await _execute_plugin_inference(task_item, file_info, plugin_info)
            add_span_event("inference_execution_completed")
            
            # 6. 保存结果和缓存
            await _save_result_and_cache(task_item, file_info, result)
            
            # 记录任务完成指标
            metrics_collector.record_task_completion(
                task_id=self.request.id,
                status="completed"
            )

            logger.info(f"推理任务完成: TaskItem {task_item_id}")
            return result
            
        except Exception as e:
            logger.error(f"推理任务失败: TaskItem {task_item_id}, 错误: {str(e)}")

            # 记录任务失败指标
            metrics_collector.record_task_completion(
                task_id=self.request.id,
                status="failed",
                error_message=str(e)
            )

            await _update_task_item_failed(task_item_id, str(e))
            
            # 重试逻辑
            if self.request.retries < self.max_retries:
                countdown = 2 ** self.request.retries
                logger.info(f"推理任务失败，{countdown}秒后重试 (第{self.request.retries + 1}次)")

                # 记录重试指标
                metrics_collector.record_task_completion(
                    task_id=self.request.id,
                    status="retried",
                    error_message=str(e),
                    retry_count=self.request.retries + 1
                )

                raise self.retry(countdown=countdown, exc=e)
            else:
                logger.error(f"推理任务最终失败，已达最大重试次数: TaskItem {task_item_id}")
                raise e
    
    return asyncio.run(_execute_inference())


async def _ensure_file_downloaded(task_item) -> Dict[str, Any]:
    """确保文件已下载到本地"""
    async with get_async_db() as db:
        if not task_item.file_id:
            # 如果没有file_id，触发文件下载
            from app.tasks.file_download import predownload_file_task
            predownload_file_task.delay(task_item.file_url, task_item.id, task_item.file_type)
            raise ValueError(f"文件尚未下载，已触发下载任务: {task_item.file_url}")
        
        # 获取文件信息
        file_info = await file_repository.get_by_id(db, task_item.file_id)
        if not file_info:
            raise ValueError(f"文件记录不存在: {task_item.file_id}")
        
        # 检查本地文件是否存在
        if not file_info.local_path:
            # 触发重新下载
            from app.core.file.manager import file_manager
            file_info = await file_manager.download_from_url(file_info.url)
        
        return {
            "id": file_info.id,
            "local_path": file_info.local_path,
            "file_hash": file_info.file_hash,
            "url": file_info.url,
            "file_type": file_info.file_type,
            "media_type": file_info.media_type
        }


async def _get_plugin_info(task_item) -> Dict[str, Any]:
    """获取插件信息"""
    async with get_async_db() as db:
        plugin = await plugin_repository.get_by_code_and_version(
            db,
            plugin_code=task_item.plugin_code,
            plugin_version=task_item.plugin_version
        )
        
        if not plugin:
            raise ValueError(f"插件不存在: {task_item.plugin_code}:{task_item.plugin_version}")
        
        if plugin.status != "enabled":
            raise ValueError(f"插件未启用: {task_item.plugin_code}:{task_item.plugin_version}")
        
        return {
            "plugin_code": plugin.plugin_code,
            "plugin_version": plugin.plugin_version,
            "engine": plugin.engine,
            "model_file_path": plugin.model_file_path,
            "config_file_path": plugin.config_file_path,
            "python_file_path": plugin.python_file_path,
            "config": plugin.config or {}
        }


async def _check_cache(task_item, file_info) -> Optional[Dict[str, Any]]:
    """检查缓存"""
    async with get_async_db() as db:
        cache_result = await CacheService.get_cache(
            db,
            file_hash=file_info["file_hash"],
            plugin_code=task_item.plugin_code,
            plugin_version=task_item.plugin_version,
            params=task_item.params
        )
        return cache_result


async def _execute_plugin_inference(task_item, file_info, plugin_info) -> Dict[str, Any]:
    """执行插件推理"""
    # 获取Worker引擎池
    engine_pool = get_worker_engine_pool()

    # 准备输入数据
    input_data = {
        "task_id": task_item.task_id,
        "task_item_id": task_item.id,
        "data_id": task_item.data_id,
        "file_type": file_info["file_type"],
        "media_type": file_info["media_type"],
        "file_path": file_info["local_path"],
        "file_url": file_info["url"],
        "params": task_item.params or {}
    }

    # 使用Worker引擎池执行插件
    async with engine_pool.get_engine_context(plugin_info) as engine:
        # 直接调用插件执行逻辑，不使用全局引擎池
        from app.plugins.loader import PluginLoader
        from app.plugins.engines.compat import PluginMethodProxy

        # 加载插件
        plugin = PluginLoader.load_plugin(plugin_info)

        # 创建插件方法代理
        plugin_proxy = PluginMethodProxy(plugin)

        # 执行插件处理
        result = await plugin_proxy.process(input_data, engine)
        return result


async def _save_result_and_cache(task_item, file_info, result: Dict[str, Any]):
    """保存结果和缓存"""
    async with get_async_db() as db:
        # 更新TaskItem结果
        await task_item_repository.update(
            db,
            db_obj=task_item,
            obj_in={
                "status": "completed",
                "result": result.get("result"),
                "process_time": result.get("process_time"),
                "completed_at": datetime.now()
            }
        )
        
        # 保存到缓存
        await CacheService.save_cache(
            db,
            file_hash=file_info["file_hash"],
            plugin_code=task_item.plugin_code,
            plugin_version=task_item.plugin_version,
            params=task_item.params,
            result=result.get("result"),
            process_time=result.get("process_time")
        )


async def _update_task_item_success(task_item_id: int, result: Dict[str, Any], from_cache: bool = False):
    """更新任务项为成功状态"""
    async with get_async_db() as db:
        task_item = await task_item_repository.get(db, id=task_item_id)
        if task_item:
            await task_item_repository.update(
                db,
                db_obj=task_item,
                obj_in={
                    "status": "completed",
                    "result": result.get("result"),
                    "process_time": result.get("process_time"),
                    "from_cache": from_cache,
                    "completed_at": datetime.now()
                }
            )


async def _update_task_item_failed(task_item_id: int, error_message: str):
    """更新任务项为失败状态"""
    async with get_async_db() as db:
        task_item = await task_item_repository.get(db, id=task_item_id)
        if task_item:
            await task_item_repository.update(
                db,
                db_obj=task_item,
                obj_in={
                    "status": "failed",
                    "error": error_message,
                    "completed_at": datetime.now()
                }
            )
