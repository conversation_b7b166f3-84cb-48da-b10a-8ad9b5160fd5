"""
文件下载相关Celery任务
"""
import asyncio
from typing import Optional

from app.core.celery_app import celery_app
from app.service.file_service import FileService
from app.repository.task_item_repository import task_item_repository
from app.core.log import task_logger as logger
from app.utils.trace_context_utils import traced_async_task, add_span_attributes
from app.core.db import get_async_db


@celery_app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3})
@traced_async_task(
    "predownload_file_task_{file_url}",
    attributes={
        "component": "file_download",
        "operation": "predownload_file"
    }
)
def predownload_file_task(self, file_url: str, task_item_id: int, file_type: Optional[str] = None):
    """
    预下载文件任务
    
    Args:
        file_url: 文件URL
        task_item_id: 任务项ID
        file_type: 文件类型
    """
    async def _predownload_file():
        try:
            # 添加span属性
            add_span_attributes(
                task_item_id=task_item_id,
                file_url=file_url,
                file_type=file_type or "unknown"
            )
            
            logger.info(f"开始预下载文件: {file_url}, TaskItem: {task_item_id}")
            
            # 下载文件
            file_service = FileService()
            file_info = await file_service.download_from_url(
                url=file_url,
                file_type=file_type,
                save_to_storage=True
            )
            
            # 更新TaskItem的file_id
            async with get_async_db() as db:
                task_item = await task_item_repository.get(db, id=task_item_id)
                if task_item:
                    await task_item_repository.update(
                        db,
                        db_obj=task_item,
                        obj_in={
                            "file_id": file_info.id,
                            "file_hash": file_info.file_hash
                        }
                    )
                    logger.info(f"文件下载完成，更新TaskItem {task_item_id}, file_id: {file_info.id}")
                    
                    # 触发推理任务
                    from app.tasks.inference import inference_task
                    inference_task.delay(task_item_id)
                else:
                    logger.error(f"TaskItem不存在: {task_item_id}")
            
            return file_info.id
            
        except Exception as e:
            logger.error(f"文件预下载失败: {file_url}, TaskItem: {task_item_id}, 错误: {str(e)}")
            
            # 更新TaskItem状态为失败
            try:
                async with get_async_db() as db:
                    task_item = await task_item_repository.get(db, id=task_item_id)
                    if task_item:
                        await task_item_repository.update(
                            db,
                            db_obj=task_item,
                            obj_in={
                                "status": "failed",
                                "error": f"文件下载失败: {str(e)}"
                            }
                        )
            except Exception as update_error:
                logger.error(f"更新TaskItem失败状态时出错: {str(update_error)}")
            
            # 重试逻辑
            if self.request.retries < self.max_retries:
                countdown = 2 ** self.request.retries  # 指数退避
                logger.info(f"文件下载失败，{countdown}秒后重试 (第{self.request.retries + 1}次)")
                raise self.retry(countdown=countdown, exc=e)
            else:
                logger.error(f"文件下载最终失败，已达最大重试次数: {file_url}")
                raise e
    
    # 运行异步函数
    return asyncio.run(_predownload_file())


@celery_app.task(bind=True)
@traced_async_task(
    "batch_predownload_files_task",
    attributes={
        "component": "file_download", 
        "operation": "batch_predownload_files"
    }
)
def batch_predownload_files_task(self, file_urls_and_task_items: list):
    """
    批量预下载文件任务
    
    Args:
        file_urls_and_task_items: [(file_url, task_item_id, file_type), ...]
    """
    async def _batch_predownload():
        try:
            logger.info(f"开始批量预下载 {len(file_urls_and_task_items)} 个文件")
            
            # 批量下载文件
            file_urls = [item[0] for item in file_urls_and_task_items]
            file_service = FileService()
            file_infos = await file_service.batch_download_from_urls(
                urls=file_urls,
                save_to_storage=True
            )
            
            # 更新TaskItem记录
            async with get_async_db() as db:
                for i, (file_url, task_item_id, file_type) in enumerate(file_urls_and_task_items):
                    if i < len(file_infos):
                        file_info = file_infos[i]
                        task_item = await task_item_repository.get(db, id=task_item_id)
                        if task_item:
                            await task_item_repository.update(
                                db,
                                db_obj=task_item,
                                obj_in={
                                    "file_id": file_info.id,
                                    "file_hash": file_info.file_hash
                                }
                            )
                            
                            # 触发推理任务
                            from app.workers.tasks.inference import inference_task
                            inference_task.delay(task_item_id)
            
            logger.info(f"批量文件下载完成，成功下载 {len(file_infos)} 个文件")
            return len(file_infos)
            
        except Exception as e:
            logger.error(f"批量文件下载失败: {str(e)}")
            raise e
    
    return asyncio.run(_batch_predownload())
