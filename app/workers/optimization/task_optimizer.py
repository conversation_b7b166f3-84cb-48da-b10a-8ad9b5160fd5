"""
任务优化器
自动优化任务分发和资源分配
"""
import asyncio
import time
from typing import Dict, List, Any
from dataclasses import dataclass

from app.workers.monitoring.celery_metrics import metrics_collector
from app.core.log import task_logger as logger


@dataclass
class OptimizationRule:
    """优化规则"""
    name: str
    condition: str  # 触发条件
    action: str     # 执行动作
    threshold: float
    enabled: bool = True


class TaskOptimizer:
    """任务优化器"""
    
    def __init__(self):
        """初始化优化器"""
        self.optimization_rules = [
            OptimizationRule(
                name="high_priority_queue_overflow",
                condition="high_priority_pending > threshold",
                action="scale_up_high_priority_workers",
                threshold=50
            ),
            OptimizationRule(
                name="worker_memory_high",
                condition="worker_memory_usage > threshold",
                action="restart_worker",
                threshold=0.9  # 90%
            ),
            OptimizationRule(
                name="queue_processing_slow",
                condition="queue_processing_rate < threshold",
                action="increase_worker_concurrency",
                threshold=0.1  # 0.1 tasks/second
            ),
            OptimizationRule(
                name="task_failure_rate_high",
                condition="task_failure_rate > threshold",
                action="reduce_worker_concurrency",
                threshold=0.1  # 10%
            )
        ]
        
        self.optimization_history = []
        self.last_optimization_time = 0
        self.optimization_interval = 60  # 60秒检查一次
        
        logger.info("任务优化器初始化完成")
    
    async def start_optimization_loop(self):
        """启动优化循环"""
        logger.info("启动任务优化循环")

        while True:
            try:
                await asyncio.sleep(self.optimization_interval)
                await self.run_optimization()
            except Exception as e:
                logger.error(f"优化循环异常: {str(e)}")
                await asyncio.sleep(self.optimization_interval)
    
    async def run_optimization(self):
        """执行优化检查"""
        current_time = time.time()
        
        # 获取当前指标
        summary = metrics_collector.get_summary_stats()
        worker_stats = metrics_collector.get_worker_stats()
        queue_stats = metrics_collector.get_queue_stats()
        
        # 检查每个优化规则
        for rule in self.optimization_rules:
            if not rule.enabled:
                continue
            
            try:
                should_optimize = await self._check_rule_condition(
                    rule, summary, worker_stats, queue_stats
                )
                
                if should_optimize:
                    await self._execute_optimization_action(
                        rule, summary, worker_stats, queue_stats
                    )
                    
                    # 记录优化历史
                    self.optimization_history.append({
                        "timestamp": current_time,
                        "rule": rule.name,
                        "action": rule.action,
                        "metrics_snapshot": {
                            "summary": summary,
                            "worker_count": len(worker_stats),
                            "queue_count": len(queue_stats)
                        }
                    })
                    
                    # 限制历史记录数量
                    if len(self.optimization_history) > 1000:
                        self.optimization_history = self.optimization_history[-500:]
                    
            except Exception as e:
                logger.error(f"执行优化规则 {rule.name} 失败: {str(e)}")
        
        self.last_optimization_time = current_time
    
    async def _check_rule_condition(
        self,
        rule: OptimizationRule,
        summary: Dict[str, Any],
        worker_stats: List[Dict[str, Any]],
        queue_stats: List[Dict[str, Any]]
    ) -> bool:
        """检查规则条件是否满足"""
        
        if rule.name == "high_priority_queue_overflow":
            high_priority_pending = sum(
                q.get('pending_tasks', 0) for q in queue_stats
                if q.get('priority', 0) >= 7
            )
            return high_priority_pending > rule.threshold
        
        elif rule.name == "worker_memory_high":
            for worker in worker_stats:
                memory_usage = worker.get('memory_usage_mb', 0)
                # 假设最大内存为2GB
                memory_ratio = memory_usage / 2048
                if memory_ratio > rule.threshold:
                    return True
            return False
        
        elif rule.name == "queue_processing_slow":
            for queue in queue_stats:
                processing_rate = queue.get('processing_rate', 0)
                if processing_rate < rule.threshold and queue.get('pending_tasks', 0) > 10:
                    return True
            return False
        
        elif rule.name == "task_failure_rate_high":
            overview = summary.get('overview', {})
            total_tasks = overview.get('total_tasks', 0)
            failed_tasks = overview.get('failed_tasks', 0)
            
            if total_tasks > 100:  # 只有足够的样本才计算
                failure_rate = failed_tasks / total_tasks
                return failure_rate > rule.threshold
            return False
        
        return False
    
    async def _execute_optimization_action(
        self,
        rule: OptimizationRule,
        summary: Dict[str, Any],
        worker_stats: List[Dict[str, Any]],
        queue_stats: List[Dict[str, Any]]
    ):
        """执行优化动作"""
        logger.info(f"执行优化动作: {rule.action} (规则: {rule.name})")
        
        if rule.action == "scale_up_high_priority_workers":
            await self._scale_up_high_priority_workers()
        
        elif rule.action == "restart_worker":
            await self._restart_high_memory_worker(worker_stats)
        
        elif rule.action == "increase_worker_concurrency":
            await self._increase_worker_concurrency()
        
        elif rule.action == "reduce_worker_concurrency":
            await self._reduce_worker_concurrency()
        
        else:
            logger.warning(f"未知的优化动作: {rule.action}")
    
    async def _scale_up_high_priority_workers(self):
        """扩容高优先级Worker"""
        # 这里应该调用容器编排系统（如Kubernetes）来扩容
        # 或者发送通知给运维人员
        logger.info("建议扩容高优先级Worker")
        
        # 示例：发送告警通知
        await self._send_optimization_alert(
            "high_priority_queue_overflow",
            "高优先级队列积压，建议增加Worker实例"
        )
    
    async def _restart_high_memory_worker(self, worker_stats: List[Dict[str, Any]]):
        """重启高内存使用的Worker"""
        high_memory_workers = [
            worker for worker in worker_stats
            if worker.get('memory_usage_mb', 0) / 2048 > 0.9
        ]
        
        for worker in high_memory_workers:
            worker_id = worker.get('worker_id')
            logger.warning(f"Worker {worker_id} 内存使用过高，建议重启")
            
            # 这里应该调用Worker管理接口来重启Worker
            await self._send_optimization_alert(
                "worker_memory_high",
                f"Worker {worker_id} 内存使用过高: {worker.get('memory_usage_mb', 0):.1f}MB"
            )
    
    async def _increase_worker_concurrency(self):
        """增加Worker并发数"""
        logger.info("建议增加Worker并发数")
        
        await self._send_optimization_alert(
            "queue_processing_slow",
            "队列处理速度较慢，建议增加Worker并发数"
        )
    
    async def _reduce_worker_concurrency(self):
        """减少Worker并发数"""
        logger.info("建议减少Worker并发数")
        
        await self._send_optimization_alert(
            "task_failure_rate_high",
            "任务失败率较高，建议减少Worker并发数"
        )
    
    async def _send_optimization_alert(self, alert_type: str, message: str):
        """发送优化告警"""
        # 这里可以集成告警系统，如钉钉、企业微信、邮件等
        logger.warning(f"优化告警 [{alert_type}]: {message}")
        
        # 示例：记录到数据库或发送到监控系统
        # await alert_service.send_alert(alert_type, message)
    
    def get_optimization_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取优化历史记录"""
        return self.optimization_history[-limit:]
    
    def get_optimization_rules(self) -> List[Dict[str, Any]]:
        """获取优化规则配置"""
        return [
            {
                "name": rule.name,
                "condition": rule.condition,
                "action": rule.action,
                "threshold": rule.threshold,
                "enabled": rule.enabled
            }
            for rule in self.optimization_rules
        ]
    
    def update_rule(self, rule_name: str, **kwargs):
        """更新优化规则"""
        for rule in self.optimization_rules:
            if rule.name == rule_name:
                for key, value in kwargs.items():
                    if hasattr(rule, key):
                        setattr(rule, key, value)
                        logger.info(f"更新优化规则 {rule_name}.{key} = {value}")
                break
        else:
            logger.warning(f"未找到优化规则: {rule_name}")


# 全局优化器实例
task_optimizer = TaskOptimizer()
