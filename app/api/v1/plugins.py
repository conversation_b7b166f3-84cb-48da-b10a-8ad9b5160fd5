"""
插件管理API
"""
from fastapi import APIRouter, Depends, File, UploadFile, HTTPException
from fastapi import status
from sqlalchemy.ext.asyncio import AsyncSession

from app.schemas.base import BaseResponse, PageResponse, PageParams
from app.schemas.plugin import PluginUpdate, PluginFilter, PluginSyncRequest, PluginResponse
from app.service.plugin_service import PluginService, PluginParseError
from app.core.db import get_db

router = APIRouter()


@router.post(
    "",
    response_model=BaseResponse[PluginResponse],
    summary="注册插件"
)
async def register_plugin(
        plugin_model_file: UploadFile = File(..., alias="pluginModelFile", description="模型文件"),
        plugin_config_file: UploadFile = File(..., alias="pluginConfigFile", description="配置文件"),
        plugin_python_file: UploadFile = File(..., alias="pluginPythonFile", description="Python处理文件"),
        db: AsyncSession = Depends(get_db)
):
    """
    注册插件
    
    - **pluginModelFile**: 模型文件
    - **pluginConfigFile**: 配置文件
    - **pluginPythonFile**: Python处理文件
    """
    try:
        result = await PluginService.register_plugin(
            db=db,
            model_file=plugin_model_file,
            config_file=plugin_config_file,
            python_file=plugin_python_file
        )

        return BaseResponse(
            data=result,
            message="插件注册成功"
        )
    except PluginParseError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"插件解析错误: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"插件注册失败: {str(e)}"
        )


@router.post(
    "/sync",
    response_model=BaseResponse[PluginResponse],
    summary="从本地路径同步插件"
)
async def sync_plugin(
        sync_request: PluginSyncRequest,
        db: AsyncSession = Depends(get_db)
):
    """
    从本地路径同步插件，验证并更新或创建插件
    
    Args:
        sync_request: 插件同步请求
        db: 数据库会话
        
    Returns:
        插件信息
    """
    try:
        result = await PluginService.sync_plugin_from_path(
            db=db,
            plugin_dir_path=sync_request.plugin_dir_path,
            update_if_newer=sync_request.update_if_newer
        )

        return BaseResponse(
            data=result,
            message="插件同步成功"
        )
    except PluginParseError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"插件解析错误: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"插件同步失败: {str(e)}"
        )


@router.get("", response_model=BaseResponse[PageResponse[PluginResponse]], summary="获取插件列表")
async def list_plugins(
        filter_params: PluginFilter = Depends(),
        page_params: PageParams = Depends(),
        db: AsyncSession = Depends(get_db)
):
    """
    获取插件列表
    
    - **filter_params**: 过滤参数
    - **page_params**: 分页参数
      - page: 页码，默认1
      - page_size: 每页数量，默认10，最大100
    """
    try:
        # 获取插件列表
        result = await PluginService.get_plugins(
            db=db,
            filter_params=filter_params,
            page_params=page_params
        )
        return BaseResponse[PageResponse[PluginResponse]](
            message="获取插件列表成功",
            data=result
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取插件列表失败: {str(e)}"
        )


@router.get("/{plugin_id}", response_model=BaseResponse[PluginResponse], summary="获取插件详情")
async def get_plugin(plugin_id: str, db: AsyncSession = Depends(get_db)):
    """
    获取插件详情
    
    - **plugin_id**: 插件ID
    """
    try:
        result = await PluginService.get_plugin(db=db, plugin_id=int(plugin_id))

        return BaseResponse(
            data=result,
            message="获取插件详情成功"
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取插件详情失败: {str(e)}"
        )


@router.put("/{plugin_id}", response_model=BaseResponse[PluginResponse], summary="更新插件信息")
async def update_plugin(
        plugin_id: str,
        plugin_update: PluginUpdate,
        db: AsyncSession = Depends(get_db)
):
    """
    更新插件信息
    
    - **plugin_id**: 插件ID
    - **plugin_update**: 更新的插件信息
    """
    try:
        result = await PluginService.update_plugin(
            db=db,
            plugin_id=int(plugin_id),
            plugin_in=plugin_update
        )

        return BaseResponse(
            data=result,
            message="插件更新成功"
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"插件更新失败: {str(e)}"
        )


@router.delete("/{plugin_id}", response_model=BaseResponse[bool], summary="删除插件")
async def delete_plugin(plugin_id: str, db: AsyncSession = Depends(get_db)):
    """
    删除插件
    
    - **plugin_id**: 插件ID
    """
    try:
        result = await PluginService.delete_plugin(db=db, plugin_id=int(plugin_id))

        return BaseResponse(
            data=result,
            message="插件删除成功"
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"插件删除失败: {str(e)}"
        )
