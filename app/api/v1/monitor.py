"""
系统监控API
整合系统状态、引擎池、Celery监控等功能
"""
from typing import Dict, Any, List

from fastapi import APIRouter, Query
from app.workers.monitoring.celery_metrics import metrics_collector
from app.schemas.base import BaseResponse
from app.core.log import app_logger as logger

router = APIRouter()


@router.get("/health", response_model=BaseResponse[Dict[str, Any]])
async def get_system_health():
    """
    系统健康检查
    
    Returns:
        BaseResponse[Dict]: 系统健康状态
    """
    try:
        # 检查Celery连接
        from celery import current_app
        inspect = current_app.control.inspect()
        
        # 检查Worker连接
        ping_result = inspect.ping(timeout=1.0)
        active_workers = list(ping_result.keys()) if ping_result else []
        
        # 获取基础指标
        summary = metrics_collector.get_summary_stats()
        
        # 计算健康状态
        total_workers = len(active_workers)
        
        # 确定健康状态
        if total_workers > 0:
            status = "healthy"
            health_score = 100
        else:
            status = "critical"
            health_score = 0
        
        health_info = {
            "status": status,
            "health_score": health_score,
            "celery": {
                "broker_connected": True if ping_result else False,
                "active_workers": total_workers,
                "worker_names": active_workers
            },
            "tasks": {
                "active": summary.get("active_tasks_count", 0),
                "completed": summary.get("overview", {}).get("completed_tasks", 0),
                "failed": summary.get("overview", {}).get("failed_tasks", 0)
            },
            "flower_url": "http://localhost:5555"  # Flower监控地址
        }
        
        return BaseResponse(
            data=health_info,
            message="获取系统健康状态成功"
        )
    except Exception as e:
        logger.error(f"获取系统健康状态失败: {str(e)}")
        return BaseResponse(
            success=False,
            data={
                "status": "unknown",
                "error": str(e),
                "flower_url": "http://localhost:5555"
            },
            message=f"获取系统健康状态失败: {str(e)}"
        )


@router.get("/health/basic", summary="基础健康检查")
async def basic_health_check():
    """基础健康检查"""
    return {"status": "ok", "message": "服务正常运行"}


@router.get("/engine-pool/stats", response_model=BaseResponse[Dict[str, Any]])
async def get_engine_pool_stats():
    """
    获取引擎池统计信息（Celery模式下为Worker引擎池）
    
    Returns:
        BaseResponse[Dict]: 引擎池统计数据
    """
    try:
        # Celery模式下获取Worker引擎池统计
        worker_stats = metrics_collector.get_worker_stats()
        
        # 汇总引擎池信息
        total_engines = 0
        engine_types = {}
        
        for worker in worker_stats:
            engine_pool_stats = worker.get('engine_pool_stats', {})
            engines = engine_pool_stats.get('engines', {})
            total_engines += len(engines)
            
            for engine_key, engine_info in engines.items():
                engine_type = engine_info.get('engine_type', 'unknown')
                engine_types[engine_type] = engine_types.get(engine_type, 0) + 1
        
        stats = {
            "mode": "celery_worker_pools",
            "total_workers": len(worker_stats),
            "total_engines": total_engines,
            "engine_types": engine_types,
            "worker_details": worker_stats
        }
        
        return BaseResponse(
            data=stats,
            message="获取引擎池统计信息成功"
        )
    except Exception as e:
        logger.error(f"获取引擎池统计信息失败: {str(e)}")
        return BaseResponse(
            success=False,
            message=f"获取引擎池统计信息失败: {str(e)}"
        )


@router.get("/metrics/summary", response_model=BaseResponse[Dict[str, Any]])
async def get_metrics_summary():
    """
    获取指标汇总信息
    
    Returns:
        BaseResponse[Dict]: 指标汇总
    """
    try:
        summary = metrics_collector.get_summary_stats()
        return BaseResponse(
            data=summary,
            message="获取指标汇总成功"
        )
    except Exception as e:
        logger.error(f"获取指标汇总失败: {str(e)}")
        return BaseResponse(
            success=False,
            message=f"获取指标汇总失败: {str(e)}"
        )


@router.get("/metrics/workers", response_model=BaseResponse[List[Dict[str, Any]]])
async def get_worker_metrics():
    """
    获取Worker指标
    
    Returns:
        BaseResponse[List[Dict]]: Worker指标列表
    """
    try:
        worker_stats = metrics_collector.get_worker_stats()
        return BaseResponse(
            data=worker_stats,
            message="获取Worker指标成功"
        )
    except Exception as e:
        logger.error(f"获取Worker指标失败: {str(e)}")
        return BaseResponse(
            success=False,
            message=f"获取Worker指标失败: {str(e)}"
        )


@router.get("/metrics/performance", response_model=BaseResponse[Dict[str, Any]])
async def get_performance_metrics():
    """
    获取性能指标
    
    Returns:
        BaseResponse[Dict]: 性能指标
    """
    try:
        performance = metrics_collector.get_performance_metrics()
        return BaseResponse(
            data=performance,
            message="获取性能指标成功"
        )
    except Exception as e:
        logger.error(f"获取性能指标失败: {str(e)}")
        return BaseResponse(
            success=False,
            message=f"获取性能指标失败: {str(e)}"
        )


@router.get("/metrics/tasks/history", response_model=BaseResponse[List[Dict[str, Any]]])
async def get_task_history(
    limit: int = Query(default=100, ge=1, le=1000, description="返回记录数量")
):
    """
    获取任务历史记录
    
    Args:
        limit: 返回记录数量限制
        
    Returns:
        BaseResponse[List[Dict]]: 任务历史记录
    """
    try:
        task_history = metrics_collector.get_task_history(limit=limit)
        return BaseResponse(
            data=task_history,
            message="获取任务历史记录成功"
        )
    except Exception as e:
        logger.error(f"获取任务历史记录失败: {str(e)}")
        return BaseResponse(
            success=False,
            message=f"获取任务历史记录失败: {str(e)}"
        )


@router.get("/celery/status", response_model=BaseResponse[Dict[str, Any]])
async def get_celery_status():
    """
    获取Celery状态信息
    
    Returns:
        BaseResponse[Dict]: Celery状态
    """
    try:
        from celery import current_app
        inspect = current_app.control.inspect()
        
        # 获取活跃的Worker
        active_workers = inspect.active()
        stats = inspect.stats()
        
        workers_info = []
        for worker_name in (active_workers.keys() if active_workers else []):
            worker_info = {
                "worker_name": worker_name,
                "active_tasks": len(active_workers.get(worker_name, [])),
                "stats": stats.get(worker_name, {}) if stats else {},
                "status": "active"
            }
            workers_info.append(worker_info)
        
        celery_status = {
            "broker_connected": True if active_workers else False,
            "total_workers": len(workers_info),
            "workers": workers_info,
            "flower_url": "http://localhost:5555"
        }
        
        return BaseResponse(
            data=celery_status,
            message="获取Celery状态成功"
        )
    except Exception as e:
        logger.error(f"获取Celery状态失败: {str(e)}")
        return BaseResponse(
            success=False,
            data={
                "broker_connected": False,
                "error": str(e),
                "flower_url": "http://localhost:5555"
            },
            message=f"获取Celery状态失败: {str(e)}"
        )
