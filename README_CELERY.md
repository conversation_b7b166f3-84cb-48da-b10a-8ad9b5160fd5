# AI平台 MVC架构 + Celery集成使用指南

## 概述

本项目已完成MVC架构重构和Celery分布式任务队列集成，提供清晰的分层架构和强大的任务处理能力。

## 架构特性

### MVC分层架构
- **Controller层**: API接口层，处理HTTP请求
- **Service层**: 业务逻辑层，处理核心业务逻辑
- **Repository层**: 数据访问层，处理数据库操作
- **Model层**: 数据模型层，定义数据结构

### Celery任务队列
- 分布式任务处理
- 10级优先级队列
- 自动重试和故障恢复
- 实时监控和指标收集

## 快速开始

### 1. 环境准备

确保已安装依赖：
```bash
pip install -r requirements.txt
```

确保Redis服务运行：
```bash
redis-server
```

### 2. 一键启动（推荐）

```bash
python scripts/start_all.py
```

这将自动启动：
- FastAPI应用 (端口8000)
- 高优先级Worker (2个并发)
- 普通Worker (4个并发)
- 后台Worker (6个并发)
- Flower监控界面 (端口5555)

### 3. 快速测试

```bash
python scripts/test_celery.py
```

这将测试：
- Redis连接状态
- API服务状态
- Celery Worker状态
- 任务创建和查询
- 监控指标获取

### 4. 手动启动

如果需要自定义配置，可以手动启动各个组件：

```bash
# 1. 启动FastAPI应用
uvicorn main:app --host 0.0.0.0 --port 8000

# 2. 启动Worker (新终端)
python scripts/start_celery_worker.py --preset normal --concurrency 4

# 3. 启动监控 (新终端)
python scripts/start_flower.py --port 5555
```

## 核心功能

### 任务管理API

**创建任务：**
```bash
curl -X POST "http://localhost:8000/api/v1/tasks/" \
  -H "Content-Type: application/json" \
  -d '{
    "items": [
      {
        "data_id": "test_001",
        "file_url": "https://example.com/image.jpg",
        "file_type": "image",
        "media_type": "image/jpeg",
        "params": {}
      }
    ],
    "plugins": [
      {
        "plugin_code": "image_classification",
        "plugin_version": "1.0.0"
      }
    ],
    "priority": 8
  }'
```

**查询任务：**
```bash
# 获取任务列表
curl "http://localhost:8000/api/v1/tasks/"

# 获取任务详情
curl "http://localhost:8000/api/v1/tasks/{task_id}"

# 获取任务项
curl "http://localhost:8000/api/v1/tasks/{task_id}/items"
```

### 监控API

**系统健康检查：**
```bash
curl "http://localhost:8000/api/v1/monitor/health"
```

**性能指标：**
```bash
# 指标汇总
curl "http://localhost:8000/api/v1/monitor/metrics/summary"

# Worker状态
curl "http://localhost:8000/api/v1/monitor/metrics/workers"

# 性能分析
curl "http://localhost:8000/api/v1/monitor/metrics/performance"
```

## 优先级队列

系统支持10级优先级队列 (0-9，9为最高)：

| 优先级 | 队列名称 | 用途 | Worker配置 |
|--------|----------|------|------------|
| 9 | priority_9 | 紧急任务 | 专用Worker |
| 8-6 | priority_8-6 | 高优先级任务 | 优先处理 |
| 5-3 | priority_5-3 | 普通任务 | 标准处理 |
| 2-0 | priority_2-0 | 后台任务 | 空闲时处理 |

## Worker配置

### 预定义配置

```bash
# 高优先级Worker (处理紧急任务)
python scripts/start_celery_worker.py --preset high

# 普通Worker (处理常规任务)
python scripts/start_celery_worker.py --preset normal

# 后台Worker (处理低优先级任务)
python scripts/start_celery_worker.py --preset low

# GPU Worker (GPU任务专用)
python scripts/start_celery_worker.py --preset gpu

# CPU Worker (CPU任务专用)
python scripts/start_celery_worker.py --preset cpu
```

### 自定义配置

```bash
# 自定义队列和并发数
python scripts/start_celery_worker.py \
  --name my_worker \
  --queues priority_5,priority_4,priority_3 \
  --concurrency 8 \
  --loglevel INFO
```

## 监控和管理

### Flower监控界面

访问 `http://localhost:5555` 查看：
- Worker状态和统计
- 任务执行情况
- 队列长度监控
- 实时任务流

### API监控

```bash
# 系统健康状态
curl "http://localhost:8000/api/v1/monitor/health"

# Celery状态
curl "http://localhost:8000/api/v1/monitor/celery/status"

# 引擎池统计
curl "http://localhost:8000/api/v1/monitor/engine-pool/stats"
```

## 架构特性

### 核心改进

1. **响应速度提升**：任务创建从5-30秒降至<100ms
2. **分布式处理**：支持多机器水平扩展
3. **智能调度**：10级优先级队列自动路由
4. **资源隔离**：Worker级引擎池管理
5. **全面监控**：实时指标和性能分析

### 数据流

```
用户请求 → 快速创建Task/TaskItem → 立即返回 (< 100ms)
     ↓
异步文件下载 → 下载完成 → 自动触发推理任务
     ↓
Worker执行推理 → 更新结果状态 → 完成
```

### 引擎池管理

- **Worker级隔离**：每个Worker进程独立管理引擎
- **智能复用**：相同类型任务复用引擎实例
- **自动清理**：空闲引擎自动回收
- **内存监控**：防止内存泄漏

## 故障排查

### 常见问题

**1. Worker无法启动**
```bash
# 检查Redis连接
redis-cli ping

# 检查环境变量
echo $CELERY_BROKER_URL

# 查看详细日志
python scripts/start_celery_worker.py --loglevel DEBUG
```

**2. 任务执行失败**
```bash
# 查看任务历史
curl "http://localhost:8000/api/v1/monitor/metrics/tasks/history?limit=10"

# 检查Worker状态
curl "http://localhost:8000/api/v1/monitor/metrics/workers"

# 查看Flower监控
# 浏览器访问 http://localhost:5555
```

**3. 队列积压**
```bash
# 查看队列状态
curl "http://localhost:8000/api/v1/monitor/celery/status"

# 增加Worker实例
python scripts/start_celery_worker.py --preset normal --concurrency 8
```

### 性能调优

**内存优化：**
- 调整 `max_tasks_per_child` 参数
- 增加引擎清理频率
- 监控内存使用趋势

**处理速度：**
- 增加Worker并发数
- 优化模型加载时间
- 使用GPU加速

## 部署建议

### 生产环境

1. **使用容器化部署**
2. **配置负载均衡**
3. **设置监控告警**
4. **定期备份Redis数据**
5. **配置日志轮转**

### 扩容策略

```bash
# 根据负载动态调整Worker数量
# 高负载时期
python scripts/start_celery_worker.py --preset high --concurrency 4
python scripts/start_celery_worker.py --preset normal --concurrency 8

# 低负载时期
python scripts/start_celery_worker.py --preset normal --concurrency 2
```

## 与原系统对比

| 特性 | 原系统 | Celery系统 |
|------|--------|------------|
| 响应时间 | 5-30秒 | < 100ms |
| 扩展性 | 单机限制 | 分布式无限扩展 |
| 监控 | 基础日志 | 专业监控面板 |
| 容错 | 手动重启 | 自动重试恢复 |
| 管理 | 命令行 | Web界面 + API |

## 总结

Celery集成为AI平台带来了：
- **高性能**：分布式处理，显著提升吞吐量
- **高可靠**：自动重试，故障自动恢复
- **易监控**：丰富的监控指标和可视化
- **易扩展**：支持水平扩展和动态调整
- **易管理**：统一的Web界面和API

通过合理配置和监控，系统可以稳定高效地处理大规模AI推理任务。
